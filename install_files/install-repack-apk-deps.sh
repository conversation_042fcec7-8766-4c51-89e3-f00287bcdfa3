#!/bin/bash

# Enable strict mode
set -euo pipefail
IFS=$'\n\t'

# Function to check if a command is available
check_command() {
    if ! command -v "$1" >/dev/null 2>&1; then
        return 1
    fi
    return 0
}

echo "📦 Installing dependencies for APK repacking on Debian..."

# Update package lists
echo "🔄 Updating package lists..."
sudo apt-get update

# Install Java (OpenJDK) for running JAR files
if ! check_command java; then
    echo "📦 Installing OpenJDK..."
    sudo apt-get install -y openjdk-17-jre
else
    echo "✅ Java is already installed"
fi

# Remove outdated apktool or aapt if installed via apt-get
if check_command apktool || check_command aapt; then
    echo "🗑️ Removing outdated apktool/aapt from apt-get..."
    sudo apt-get remove -y apktool aapt 2>/dev/null || true
fi

# Install apktool 2.11.1 from official release
if ! check_command apktool || [ "$(apktool --version 2>/dev/null)" != "2.11.1" ]; then
    echo "📦 Installing apktool 2.11.1..."
    sudo wget https://github.com/iBotPeaches/Apktool/releases/download/v2.11.1/apktool_2.11.1.jar -O /usr/local/bin/apktool.jar
    sudo wget https://raw.githubusercontent.com/iBotPeaches/Apktool/master/scripts/linux/apktool -O /usr/local/bin/apktool
    sudo chmod +x /usr/local/bin/apktool
else
    echo "✅ apktool 2.11.1 is already installed"
fi

# Install Android SDK build-tools for aapt/aapt2
if ! check_command aapt; then
    echo "📦 Installing Android SDK build-tools (aapt/aapt2)..."
    sudo mkdir -p /opt/android-sdk/build-tools/34
    wget https://dl.google.com/android/repository/build-tools_r34-linux.zip -O /tmp/build-tools_r34.zip
    sudo unzip /tmp/build-tools_r34.zip -d /opt/android-sdk/build-tools
    sudo chmod +x /opt/android-sdk/build-tools/34/aapt /opt/android-sdk/build-tools/34/aapt2
    rm /tmp/build-tools_r34.zip
    # Add build-tools to PATH
    if ! grep -q "android-sdk/build-tools/34" ~/.bashrc; then
        echo 'export PATH=$PATH:/opt/android-sdk/build-tools/34' | sudo tee -a ~/.bashrc
    fi
    export PATH=$PATH:/opt/android-sdk/build-tools/34
else
    echo "✅ aapt is already installed"
fi

# Install core utilities (realpath is part of coreutils)
if ! check_command realpath; then
    echo "📦 Installing coreutils..."
    sudo apt-get install -y coreutils
else
    echo "✅ coreutils (realpath) is already installed"
fi

# Install bc for size calculations
if ! check_command bc; then
    echo "📦 Installing bc..."
    sudo apt-get install -y bc
else
    echo "✅ bc is already installed"
fi

# Check if gstat is needed (optional, only if user prefers GNU stat)
if ! check_command gstat; then
    echo "📦 Installing GNU coreutils for gstat (optional)..."
    sudo apt-get install -y coreutils
else
    echo "✅ gstat is already installed"
fi

# Verify installations
echo "🔍 Verifying installed dependencies..."
for cmd in java apktool aapt realpath bc; do
    if check_command "$cmd"; then
        echo "✅ $cmd is installed"
    else
        echo "❌ Failed to install $cmd"
        exit 1
    fi
done

echo "✅ All dependencies for APK repacking have been installed successfully!"
echo "ℹ️ Note: You still need to provide signer.jar and a valid keystore file."
echo "ℹ️ Note: Run 'source ~/.bashrc' to update PATH for this session, or open a new terminal."