import { sveltekit } from '@sveltejs/kit/vite';
import { defineConfig } from 'vite';
import { resolve } from 'path';
import fs from 'fs';
import path from 'path';

export default defineConfig({
	plugins: [
		sveltekit(),
		{
			name: 'copy-repack-apk',
			apply: 'build',
			enforce: 'post',
			generateBundle() {
				try {
					// Get all files in the _repack_apk directory
					const repackDir = resolve('./_repack_apk');

					// Check if directory exists
					if (!fs.existsSync(repackDir)) {
						console.warn('Warning: _repack_apk directory not found, skipping copy');
						return;
					}

					const files = fs.readdirSync(repackDir);

					// Copy each file to the build output
					files.forEach(file => {
						const filePath = path.join(repackDir, file);
						const stats = fs.statSync(filePath);

						// Skip directories and .DS_Store files
						if (stats.isDirectory() || file === '.DS_Store') {
							return;
						}

						// Read the file and emit it as an asset
						// Copy to both potential locations to ensure it's available
						// 1. Root build directory
						this.emitFile({
							type: 'asset',
							fileName: `_repack_apk/${file}`,
							source: fs.readFileSync(filePath)
						});

						// 2. Server directory (for adapter-bun and other adapters)
						this.emitFile({
							type: 'asset',
							fileName: `server/_repack_apk/${file}`,
							source: fs.readFileSync(filePath)
						});

						// Make shell scripts executable in the output
						if (file.endsWith('.sh')) {
							// We can't set permissions directly in Vite,
							// but we can log a message to remind the user
							console.log(`Note: You may need to make ${file} executable after build`);
						}
					});
				} catch (error) {
					console.error('Error in copy-repack-apk plugin:', error);
				}
			}
		},
		{
			name: 'copy-install-files',
			apply: 'build',
			enforce: 'post',
			generateBundle(outputOptions) {
				try {
					// Determine build output directory and parent install dir
					const outputDir = outputOptions.dir || 'dist';
					const parentInstallDir = path.resolve(outputDir, '..', 'install_files');
					if (!fs.existsSync(parentInstallDir)) fs.mkdirSync(parentInstallDir, { recursive: true });

					// Get all files in the install_files directory
					const installDir = resolve('./install_files');

					// Check if directory exists
					if (!fs.existsSync(installDir)) {
						console.warn('Warning: install_files directory not found');
						return;
					}

					const files = fs.readdirSync(installDir);

					// Copy each file to the build output
					files.forEach(file => {
						const filePath = path.join(installDir, file);
						const stats = fs.statSync(filePath);

						// Skip directories and .DS_Store files
						if (stats.isDirectory() || file === '.DS_Store' || file === '.gitkeep') {
							return;
						}

						// Read the file and emit it as an asset
						// Copy to both potential locations to ensure it's available
						// 1. Root build directory
						this.emitFile({
							type: 'asset',
							fileName: `install_files/${file}`,
							source: fs.readFileSync(filePath)
						});

						// Emit to root install_files instead of server directory
						this.emitFile({
							type: 'asset',
							fileName: `server/install_files/${file}`,
							source: fs.readFileSync(filePath)
						});

						// 3. Copy directly one level up from build output
						fs.copyFileSync(filePath, path.join(parentInstallDir, file));

						// Make shell scripts and binaries executable in the output
						if (file.endsWith('.sh') || file === 'phantom-vpn-node') {
							// We can't set permissions directly in Vite,
							// but we can log a message to remind the user
							console.log(`Note: You may need to make install_files/${file} executable after build`);
						}
					});

					console.log('Successfully copied install_files to build output');
				} catch (error) {
					console.error('Error in copy-install-files plugin:', error);
				}
			}
		}
	]
});
