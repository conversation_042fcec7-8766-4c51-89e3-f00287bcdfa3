# Phantom

Project consists of admin dashboard, customer webview and api

### Install on vpn node running wireguard
```shell
curl -sL https://3geese.net/install/phantom-vpn-node.sh | bash
```

### Install apk repacking dependencies
```shell
curl -sL https://3geese.net/install/install-repack-apk-deps.sh | bash
```

### Repack an apk example
```shell
./repack_apk.sh vpn.apk -f vpn.conf -n test.apk -k key.keystore -s signer.jar -a key0 -p 123321
```

# Bun + ElysiaJS + Vite + SvelteKit Template

## About

### Why Bun?

It's a new, fast javascript runtime that is many orders of magnitude faster than Node.

### Why ElysiaJS ?

It's the best backend web server for Bun. It's akin to Express.js and is well supported. You'll notice later that we are
using SvelteKit. SvelteKit doesn't require Bun or ElysiaJS. We use Bun because it's fast. We use ElysiaJS because it offers true
backend web service functionality. You can write websockets in ElysiaJS backend, but you can not easily, if at all, with SvelteKit.

### Why Vite ?

In short, Vite offers a hot-reload for Svelte, among other nice bundler things. Svelte by itself does some of these things, but it is
primarily a compiler that produces javascript from svelte files. Vite is a bit more versatile when it comes to bundling and serving.

### Why SvelteKit ?

Svelte is such a clean, fast and reactive way to make the frontend for web sites. SvelteKit brings routing, SSR, and a framework to Svelte to
deal with many common web frontend needs. While SvelteKit doesn't require Bun or ElysiaJS, the addition of those means a faster service with
more capabilities (websockets, for example) than without.

## System Requirements

### Bun

You'll need to get Bun installed. At this point in time, Bun v1.0.25 is the latest. See: [https://bun.sh](https://bun.sh) to download.

## Getting Started

Install the dependencies:

```shell
bun install
```

Start the development server:

```shell
bun run dev
```

Bundle for production deployment:

```shell
bun run build
cd build
bun start
```

When bundling for production, you'll probably want to use a Bun docker image and copy the build into that before executing start.

## Reference

-   [Bun](https://bun.sh)
-   [ElysiaJS](https://elysiajs.com)
-   [Vite](https://vitejs.dev/)
-   [Svelte](https://svelte.dev/)
-   [SvelteKit](https://kit.svelte.dev/)
