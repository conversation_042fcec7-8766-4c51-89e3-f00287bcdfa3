<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
    />
    <title>Phantom - Offline</title>
    <style>
      html,
      body {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100%;
        overflow: hidden;
        font-family: "Segoe UI", sans-serif;
        background-color: #000;
        color: #fff;
      }

      body {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100%;
        text-align: center;
        box-sizing: border-box;
        padding: 20px;
      }

      .logo {
        width: 42px;
        height: auto;
      }

      h1 {
        font-size: 1.5rem;
        margin-bottom: 1rem;
      }

      p {
        font-size: 1rem;
        margin-bottom: 2rem;
        color: #aaa;
        max-width: 80%;
      }

      .button {
        background: linear-gradient(to right, #5b6eff, #ec6fcd);
        color: white;
        padding: 0.7rem 1.5rem;
        border: none;
        border-radius: 8px;
        font-size: 1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        box-shadow: 0 0 10px rgba(236, 111, 205, 0.3);
      }
    </style>
  </head>
  <body>
    <img src="/favicon.webp" alt="Phantom Logo" class="logo" />
    <h1>You're Offline</h1>
    <p>Please check your internet connection and try again!</p>
    <button class="button" onclick="window.location.reload()">Retry</button>

    <script>
      // Check if we're back online
      window.addEventListener("online", () => {
        window.location.reload();
      });
    </script>
  </body>
</html>
