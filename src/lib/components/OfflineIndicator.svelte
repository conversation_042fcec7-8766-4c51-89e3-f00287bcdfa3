<script>
  import { browser } from '$app/environment';
  import { onMount } from 'svelte';

  // Track offline status
  let isOffline = false;

  // Simple client-side script to check offline status
  if (browser) {
    // Set initial status based on navigator.onLine
    isOffline = !navigator.onLine;

    // Add event listeners for online/offline events
    window.addEventListener('online', () => {
      isOffline = false;
    });

    window.addEventListener('offline', () => {
      isOffline = true;
    });

    // Listen for offline status messages from the service worker
    onMount(() => {
      // Listen for messages from the service worker
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'OFFLINE_STATUS') {
          isOffline = event.data.offline;
        }
      });

      // Request current offline status from service worker
      if (navigator.serviceWorker.controller) {
        navigator.serviceWorker.controller.postMessage({
          type: 'CHECK_OFFLINE_STATUS'
        });
      }
    });
  }
</script>

<!-- Always render the banner, but hide it with CSS when offline status changes -->
{#if browser}
  <!-- Full screen overlay that appears when offline -->
  <div class="offline-overlay" class:visible={isOffline}></div>

  <!-- Offline banner notification -->
  <div class="offline-banner" class:visible={isOffline}>
    <div class="offline-icon">⚠️</div>
    <div class="offline-message">You are offline. Check your internet connection.</div>
  </div>
{/if}

<style>
  /* Full screen overlay styles */
  .offline-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5); /* Semi-transparent black */
    z-index: 9998; /* Just below the banner */
    opacity: 0;
    pointer-events: none; /* Allow clicks to pass through when invisible */
    transition: opacity 0.3s ease-out;
  }

  .offline-overlay.visible {
    opacity: 1;
    pointer-events: all; /* Block clicks when visible */
  }

  /* Banner styles */
  .offline-banner {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background-color: #8b0000; /* Dark red background */
    color: white;
    padding: 12px 20px; /* Increased padding */
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999; /* Above the overlay */
    font-family: 'Segoe UI', sans-serif;
    font-size: 18px; /* Larger font size */
    font-weight: 500;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3); /* Enhanced shadow */
    transform: translateY(-100%);
    transition: transform 0.3s ease-out;
  }

  .offline-banner.visible {
    transform: translateY(0);
  }

  .offline-icon {
    margin-right: 12px; /* Increased spacing */
    font-size: 20px; /* Larger icon */
  }

  .offline-message {
    font-weight: 600; /* Bolder text */
    letter-spacing: 0.2px; /* Improved readability */
  }

  @media (max-width: 600px) {
    .offline-banner {
      font-size: 16px; /* Still larger than original even on mobile */
      padding: 10px 16px;
    }

    .offline-icon {
      font-size: 18px;
    }
  }
</style>
