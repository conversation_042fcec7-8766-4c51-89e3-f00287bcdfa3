/**
 * APK Repacker Service
 *
 * This service provides functionality for repacking APK files with custom configurations.
 * It handles file validation, job management, and the repacking process.
 */
import fs from 'fs';
import path from 'path';
import { exec } from 'child_process';
import { promisify } from 'util';
import { fileURLToPath } from 'url';

// Get the directory name equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const execPromise = promisify(exec);

// Constants
const TMP_DIR = path.join(process.cwd(), 'tmp_apk_repacker');

// Determine if we're in development or production mode
const isDev = process.env.NODE_ENV === 'development';

// Helper function to find the repack_apk directory
function findRepackApkDir() {
  // Try multiple possible locations for the _repack_apk directory
  const possiblePaths = [
    // Most likely production paths based on error message
    path.join(process.cwd(), 'build', 'server', '_repack_apk'),
    path.join(process.cwd(), 'server', '_repack_apk'),

    // Development path (project root)
    path.join(process.cwd(), '_repack_apk'),

    // Other production paths (various possibilities)
    path.join(process.cwd(), 'build', '_repack_apk'),

    // For SvelteKit adapter-bun output
    path.join(process.cwd(), '.svelte-kit', 'adapter-bun', '_repack_apk'),
    path.join(process.cwd(), '.svelte-kit', 'adapter-bun', 'server', '_repack_apk'),

    // For SvelteKit adapter-node output
    path.join(process.cwd(), '.svelte-kit', 'output', '_repack_apk'),
    path.join(process.cwd(), '.svelte-kit', 'output', 'server', '_repack_apk'),
    path.join(process.cwd(), '.svelte-kit', 'build', '_repack_apk'),
    path.join(process.cwd(), '.svelte-kit', 'build', 'server', '_repack_apk'),

    // Alternative paths (relative to current file)
    path.resolve(__dirname, '../../../_repack_apk'),
    path.resolve(__dirname, '../../../../_repack_apk'),
    path.resolve(__dirname, '../../../server/_repack_apk'),
    path.resolve(__dirname, '../../../../server/_repack_apk'),

    // For paths relative to the current module
    path.resolve(path.dirname(fileURLToPath(import.meta.url)), '../../../_repack_apk'),
    path.resolve(path.dirname(fileURLToPath(import.meta.url)), '../../../server/_repack_apk'),

    // For paths relative to the build directory
    path.resolve(process.cwd(), '../_repack_apk'),
    path.resolve(process.cwd(), '../server/_repack_apk')
  ];

  // Log all paths we're checking
  console.log('[APK Repacker] Checking possible paths for _repack_apk directory:');
  possiblePaths.forEach(p => console.log(`- ${p}`));

  // Find the first path that exists and contains the repack_apk.sh script
  for (const p of possiblePaths) {
    if (fs.existsSync(p) && fs.existsSync(path.join(p, 'repack_apk.sh'))) {
      console.log(`[APK Repacker] Found valid _repack_apk directory at: ${p}`);
      return p;
    }
  }

  // If no valid path is found, try to create a fallback
  const defaultPath = path.join(process.cwd(), '_repack_apk');
  console.log(`[APK Repacker] No valid _repack_apk directory found, attempting to create fallback at: ${defaultPath}`);

  // Try to create the directory if it doesn't exist
  try {
    if (!fs.existsSync(defaultPath)) {
      fs.mkdirSync(defaultPath, { recursive: true });
      console.log(`[APK Repacker] Created fallback directory: ${defaultPath}`);
    }

    // Try to find the script in various possible source directories
    const possibleSourceDirs = [
      // Check server directory first (based on error message)
      path.resolve(process.cwd(), 'build/server/_repack_apk'),
      path.resolve(process.cwd(), 'server/_repack_apk'),
      // Then check other possible locations
      path.resolve(process.cwd(), '../_repack_apk'),
      path.resolve(process.cwd(), 'build/_repack_apk'),
      path.resolve(process.cwd(), '../build/server/_repack_apk')
    ];

    console.log('[APK Repacker] Checking possible source directories:');
    possibleSourceDirs.forEach(dir => console.log(`- ${dir}`));

    let sourceDir = null;
    let sourceScriptPath = null;

    // Find the first directory that contains repack_apk.sh
    for (const dir of possibleSourceDirs) {
      const scriptPath = path.join(dir, 'repack_apk.sh');
      if (fs.existsSync(scriptPath)) {
        sourceDir = dir;
        sourceScriptPath = scriptPath;
        console.log(`[APK Repacker] Found source directory: ${sourceDir}`);
        break;
      }
    }

    if (sourceDir && sourceScriptPath) {
      // Copy the script to the fallback directory
      const targetScriptPath = path.join(defaultPath, 'repack_apk.sh');
      fs.copyFileSync(sourceScriptPath, targetScriptPath);
      fs.chmodSync(targetScriptPath, '755');
      console.log(`[APK Repacker] Copied repack_apk.sh from ${sourceScriptPath} to ${targetScriptPath}`);

      // Try to copy other necessary files
      const filesToCopy = ['key.keystore', 'signer.jar'];
      for (const file of filesToCopy) {
        const sourcePath = path.join(sourceDir, file);
        if (fs.existsSync(sourcePath)) {
          const targetPath = path.join(defaultPath, file);
          fs.copyFileSync(sourcePath, targetPath);
          console.log(`[APK Repacker] Copied ${file} from ${sourcePath} to ${targetPath}`);
        } else {
          console.error(`[APK Repacker] Could not find source file: ${sourcePath}`);
        }
      }
    } else {
      console.error(`[APK Repacker] Could not find source script in any of the checked directories`);
    }
  } catch (err) {
    console.error(`[APK Repacker] Error creating fallback: ${err.message}`);
  }

  return defaultPath;
}

// Lazy-loaded paths to avoid file system operations during module import
let REPACK_APK_DIR = null;
let REPACK_SCRIPT = null;

// Function to initialize paths when needed
function initializePaths() {
  if (REPACK_APK_DIR === null) {
    REPACK_APK_DIR = findRepackApkDir();
    REPACK_SCRIPT = path.join(REPACK_APK_DIR, 'repack_apk.sh');
  }
  return { REPACK_APK_DIR, REPACK_SCRIPT };
}

// Maximum age of temporary files
const MAX_AGE = 24 * 60 * 60 * 1000; // 24 hours in milliseconds
const ONE_HOUR = 60 * 60 * 1000;     // 1 hour in milliseconds
const FIVE_MINUTES = 5 * 60 * 1000;  // 5 minutes in milliseconds

/**
 * Initialize the APK Repacker service
 */
function initialize() {
  // Initialize paths first
  const { REPACK_APK_DIR: repackDir, REPACK_SCRIPT: repackScript } = initializePaths();

  // Log environment information
  console.log(`[APK Repacker] Initializing in ${isDev ? 'development' : 'production'} mode`);
  console.log(`[APK Repacker] REPACK_APK_DIR: ${repackDir}`);
  console.log(`[APK Repacker] REPACK_SCRIPT: ${repackScript}`);

  // Ensure the temp directory exists
  try {
    if (!fs.existsSync(TMP_DIR)) {
      fs.mkdirSync(TMP_DIR, { recursive: true });
      console.log(`[APK Repacker] Created temp directory: ${TMP_DIR}`);
    }
  } catch (error) {
    console.error(`[APK Repacker] Failed to create temp directory: ${error.message}`);
  }

  // Check if the script exists
  if (fs.existsSync(repackScript)) {
    console.log(`[APK Repacker] Found repack script at: ${repackScript}`);

    // Ensure the script is executable
    try {
      fs.chmodSync(repackScript, '755');
      console.log(`[APK Repacker] Made script executable`);
    } catch (error) {
      console.error(`[APK Repacker] Failed to make script executable: ${error.message}`);
    }
  } else {
    console.error(`[APK Repacker] Repack script not found at: ${repackScript}`);
  }

  // Check if keystore and signer.jar exist
  const keystore = path.join(repackDir, 'key.keystore');
  const signerJar = path.join(repackDir, 'signer.jar');

  if (fs.existsSync(keystore)) {
    console.log(`[APK Repacker] Found keystore at: ${keystore}`);
  } else {
    console.error(`[APK Repacker] Keystore not found at: ${keystore}`);
  }

  if (fs.existsSync(signerJar)) {
    console.log(`[APK Repacker] Found signer.jar at: ${signerJar}`);
  } else {
    console.error(`[APK Repacker] signer.jar not found at: ${signerJar}`);
  }

  // Schedule regular cleanup
  scheduleRegularCleanup();
}

/**
 * Schedule regular cleanup of temporary files
 */
function scheduleRegularCleanup() {
  // Initial cleanup on startup
  try {
    const completedJobsRemoved = cleanupCompletedJobs();
    if (completedJobsRemoved > 0) {
      console.log(`[APK Repacker] Initial cleanup: Removed ${completedJobsRemoved} completed jobs`);
    }

    const oldFilesRemoved = cleanupTempFiles();
    if (oldFilesRemoved > 0) {
      console.log(`[APK Repacker] Initial cleanup: Removed ${oldFilesRemoved} old directories`);
    }
  } catch (error) {
    console.error('[APK Repacker] Error during initial cleanup:', error);
  }

  // Schedule regular cleanup every hour
  const ONE_HOUR_MS = 60 * 60 * 1000;
  setInterval(() => {
    try {
      const completedJobsRemoved = cleanupCompletedJobs();
      if (completedJobsRemoved > 0) {
        console.log(`[APK Repacker] Scheduled cleanup: Removed ${completedJobsRemoved} completed jobs`);
      }

      const oldFilesRemoved = cleanupTempFiles();
      if (oldFilesRemoved > 0) {
        console.log(`[APK Repacker] Scheduled cleanup: Removed ${oldFilesRemoved} old directories`);
      }
    } catch (error) {
      console.error('[APK Repacker] Error during scheduled cleanup:', error);
    }
  }, ONE_HOUR_MS);
}

// Note: Initialization is now lazy-loaded to avoid file system operations during module import

/**
 * Clean up old temporary files
 * @param {number} [maxAgeMs] - Maximum age of files to keep in milliseconds (default: 24 hours)
 * @returns {number} Number of directories removed
 */
function cleanupTempFiles(maxAgeMs = MAX_AGE) {
  try {
    // Create temp directory if it doesn't exist
    if (!fs.existsSync(TMP_DIR)) {
      fs.mkdirSync(TMP_DIR, { recursive: true });
      return 0;
    }

    const now = Date.now();
    let removedCount = 0;

    // Read all directories in the temp directory
    const dirs = fs.readdirSync(TMP_DIR);

    for (const dir of dirs) {
      try {
        // Skip non-numeric directories (job IDs are timestamps)
        if (!/^\d+$/.test(dir)) {
          continue;
        }

        const dirPath = path.join(TMP_DIR, dir);
        const stats = fs.statSync(dirPath);

        // Check if the directory is older than the specified age
        if (now - stats.mtimeMs > maxAgeMs) {
          // Remove the directory and its contents
          fs.rmSync(dirPath, { recursive: true, force: true });
          removedCount++;
          console.log(`Removed old APK repacking directory: ${dir}`);
        }
      } catch (err) {
        console.error(`Error processing directory ${dir}:`, err);
      }
    }

    return removedCount;
  } catch (err) {
    console.error('Error cleaning up temporary files:', err);
    return 0;
  }
}

/**
 * Clean up completed job directories that are older than 1 hour
 * @returns {number} Number of directories removed
 */
function cleanupCompletedJobs() {
  // Use a shorter time period (1 hour) for completed jobs
  return cleanupTempFiles(ONE_HOUR);
}

/**
 * Clean up all job directories except the most recent ones (last 5 minutes)
 * This is more aggressive and should be used with caution
 * @returns {number} Number of directories removed
 */
function forceCleanupAllExceptRecent() {
  // Only keep jobs from the last 5 minutes
  return cleanupTempFiles(FIVE_MINUTES);
}

/**
 * Validate an APK file
 * @param {File|Buffer} apkFile - The APK file to validate
 * @returns {{isValid: boolean, error?: string, details?: string}} Validation result
 */
async function validateApkFile(apkFile) {
  try {
    // Convert to buffer if it's a File object
    let apkBuffer;
    if (apkFile instanceof Buffer) {
      apkBuffer = apkFile;
    } else {
      // Assume it's a File object
      apkBuffer = Buffer.from(await apkFile.arrayBuffer());
    }

    // Check if the file is a valid ZIP file (APK files are ZIP files)
    // ZIP files start with the magic bytes 'PK\x03\x04'
    if (apkBuffer.length < 4 ||
        apkBuffer[0] !== 0x50 || // 'P'
        apkBuffer[1] !== 0x4B || // 'K'
        apkBuffer[2] !== 0x03 ||
        apkBuffer[3] !== 0x04) {
      return {
        isValid: false,
        error: 'The file is not a valid APK file',
        details: 'The file does not have the correct APK/ZIP format signature.'
      };
    }

    return { isValid: true };
  } catch (err) {
    return {
      isValid: false,
      error: 'Error validating APK file',
      details: err instanceof Error ? err.message : String(err)
    };
  }
}

/**
 * Create a new APK repacking job
 * @param {File|Buffer} apkFile - The APK file to repack
 * @param {string} config - The configuration to embed in the APK
 * @param {string} [assetFilename='vpn.conf'] - The filename to use for the asset in the APK
 * @param {string} [customJobId] - Optional custom job ID (defaults to timestamp)
 * @returns {Promise<{success: boolean, jobId?: string, apkName?: string, error?: string, details?: any, timeInSeconds?: number, message?: string}>}
 */
async function createRepackingJob(apkFile, config, assetFilename = 'vpn.conf', customJobId) {
  try {
    // Initialize paths if needed
    const { REPACK_APK_DIR: repackDir, REPACK_SCRIPT: repackScript } = initializePaths();

    // Start timing the repacking process
    const startTime = Date.now();

    // Create a unique job ID for this repacking job
    const jobId = customJobId || Date.now().toString();
    const jobDir = path.join(TMP_DIR, jobId);

    // Create job directory
    fs.mkdirSync(jobDir, { recursive: true });

    // Validate the APK file
    const validation = await validateApkFile(apkFile);
    if (!validation.isValid) {
      // Clean up the job directory
      try {
        fs.rmSync(jobDir, { recursive: true, force: true });
      } catch (err) {
        console.error(`Failed to clean up job directory: ${err instanceof Error ? err.message : String(err)}`);
      }

      return {
        success: false,
        error: validation.error,
        details: validation.details
      };
    }

    // Save the uploaded APK file
    const apkPath = path.join(jobDir, 'input.apk');
    let apkBuffer;

    if (apkFile instanceof Buffer) {
      apkBuffer = apkFile;
    } else {
      // Assume it's a File object
      apkBuffer = Buffer.from(await apkFile.arrayBuffer());
    }

    fs.writeFileSync(apkPath, apkBuffer);

    // Save the configuration to a file with the specified asset filename
    // Sanitize the asset filename to prevent directory traversal
    const sanitizedAssetFilename = path.basename(assetFilename || 'vpn.conf');
    const configPath = path.join(jobDir, sanitizedAssetFilename);
    fs.writeFileSync(configPath, String(config));

    // Extract the base APK name from the input file
    let baseApkName = '';
    if (apkFile instanceof Buffer) {
      // If it's a buffer, check if it has a name property (added by the server)
      try {
        // @ts-ignore - Check for custom name property
        if (apkFile.name) {
          // @ts-ignore - Use the custom name property
          baseApkName = path.basename(apkFile.name, '.apk');
        } else {
          // Fallback to a generic name with timestamp
          baseApkName = `template-${Date.now().toString().slice(-6)}`;
        }
      } catch (err) {
        // Fallback to a generic name
        baseApkName = `template-${Date.now().toString().slice(-6)}`;
      }
    } else {
      // If it's a File object, get the name from the file
      try {
        // Remove .apk extension if present
        // @ts-ignore - File objects have a name property
        const fileName = apkFile.name || 'base';
        baseApkName = path.basename(fileName, '.apk');
      } catch (err) {
        // Fallback to a default name
        baseApkName = 'base';
      }
    }

    // Generate a short timestamp (last 4 digits of the job ID or current timestamp)
    const shortTimestamp = jobId.slice(-4) || Date.now().toString().slice(-4);

    // Create a new output APK name with the base name and timestamp
    const outputApkName = `${baseApkName}-${shortTimestamp}.apk`;
    const outputApkPath = path.join(jobDir, outputApkName);

    // Execute the repack_apk.sh script
    const keystore = path.join(repackDir, 'key.keystore');
    const signerJar = path.join(repackDir, 'signer.jar');

    // Verify that required files exist before proceeding
    if (!fs.existsSync(keystore)) {
      return {
        success: false,
        error: 'Keystore file not found',
        details: `The keystore file does not exist at: ${keystore}`
      };
    }

    if (!fs.existsSync(signerJar)) {
      return {
        success: false,
        error: 'Signer JAR file not found',
        details: `The signer JAR file does not exist at: ${signerJar}`
      };
    }

    if (!fs.existsSync(repackScript)) {
      return {
        success: false,
        error: 'Repack script not found',
        details: `The repack script does not exist at: ${repackScript}`
      };
    }

    // Make sure the script is executable
    try {
      fs.chmodSync(repackScript, '755');
    } catch (err) {
      console.error(`Failed to make script executable: ${err instanceof Error ? err.message : String(err)}`);
    }

    // Create a unique temporary directory for this job to avoid conflicts
    // when multiple jobs run simultaneously
    const uniqueTmpDir = path.join(jobDir, 'tmp_apk');

    // Create the unique temporary directory
    try {
      fs.mkdirSync(uniqueTmpDir, { recursive: true });
      console.log(`[APK Repacker] Created unique temporary directory: ${uniqueTmpDir}`);
    } catch (err) {
      console.error(`[APK Repacker] Failed to create unique temporary directory: ${err.message}`);
    }

    // Pass the unique temporary directory to the script using the TMP_DIR environment variable
    const cmd = `TMP_DIR="${uniqueTmpDir}" ${repackScript} ${apkPath} -f ${configPath} -n ${outputApkName} -o ${jobDir} -k ${keystore} -s ${signerJar} -a key0 -p 123321 -d ${sanitizedAssetFilename}`;

    // Log the command (with password masked)
    console.log(`[APK Repacker] Executing command: ${cmd.replace('-p 123321', '-p ******').replace(uniqueTmpDir, 'UNIQUE_TMP_DIR')}`);

    // Verify all files exist before executing
    console.log(`[APK Repacker] Verifying files before execution:`);
    console.log(`- APK file: ${fs.existsSync(apkPath) ? 'EXISTS' : 'MISSING'}`);
    console.log(`- Config file: ${fs.existsSync(configPath) ? 'EXISTS' : 'MISSING'}`);
    console.log(`- Repack script: ${fs.existsSync(repackScript) ? 'EXISTS' : 'MISSING'}`);
    console.log(`- Keystore: ${fs.existsSync(keystore) ? 'EXISTS' : 'MISSING'}`);
    console.log(`- Signer JAR: ${fs.existsSync(signerJar) ? 'EXISTS' : 'MISSING'}`);
    console.log(`- Output directory: ${fs.existsSync(jobDir) ? 'EXISTS' : 'MISSING'}`);

    try {
      console.log(`[APK Repacker] Starting repacking process...`);
      const { stdout, stderr } = await execPromise(cmd);
      console.log(`[APK Repacker] Command completed`);

      // Log stdout and stderr (truncated if too long)
      if (stdout) {
        const truncatedStdout = stdout.length > 1000 ? stdout.substring(0, 1000) + '...(truncated)' : stdout;
        console.log(`[APK Repacker] Command output: ${truncatedStdout}`);
      }

      if (stderr) {
        const truncatedStderr = stderr.length > 1000 ? stderr.substring(0, 1000) + '...(truncated)' : stderr;
        console.error(`[APK Repacker] Command error output: ${truncatedStderr}`);
      }

      // Clean up the unique temporary directory
      try {
        if (fs.existsSync(uniqueTmpDir)) {
          fs.rmSync(uniqueTmpDir, { recursive: true, force: true });
          console.log(`[APK Repacker] Cleaned up unique temporary directory: ${uniqueTmpDir}`);
        }
      } catch (cleanupErr) {
        console.error(`[APK Repacker] Failed to clean up unique temporary directory: ${cleanupErr instanceof Error ? cleanupErr.message : String(cleanupErr)}`);
      }

      // Check if the output APK was created
      if (!fs.existsSync(outputApkPath)) {
        console.error('[APK Repacker] Failed to create repacked APK');
        return {
          success: false,
          error: 'Failed to create repacked APK',
          details: { stdout, stderr }
        };
      }

      console.log(`[APK Repacker] Successfully created repacked APK at: ${outputApkPath}`);

      // Calculate elapsed time in seconds
      const endTime = Date.now();
      const timeInSeconds = ((endTime - startTime) / 1000).toFixed(2);
      console.log(`[APK Repacker] Repacking completed in ${timeInSeconds} seconds`);

      // Success - return the job ID, APK name, and timing information
      return {
        success: true,
        message: 'APK repacked successfully',
        jobId,
        apkName: outputApkName,
        timeInSeconds: parseFloat(timeInSeconds)
      };
    } catch (err) {
      // Clean up the unique temporary directory in case of error
      try {
        if (fs.existsSync(uniqueTmpDir)) {
          fs.rmSync(uniqueTmpDir, { recursive: true, force: true });
          console.log(`[APK Repacker] Cleaned up unique temporary directory after error: ${uniqueTmpDir}`);
        }
      } catch (cleanupErr) {
        console.error(`[APK Repacker] Failed to clean up unique temporary directory after error: ${cleanupErr instanceof Error ? cleanupErr.message : String(cleanupErr)}`);
      }

      console.error('Error executing repack script:', err);
      // Extract error details
      let errorMessage = 'Error executing repack script';
      let errorDetailsObj = {
        message: 'Unknown error occurred'
      };

      if (err instanceof Error) {
        errorDetailsObj.message = err.message || 'No error message available';

        // Check if stdout/stderr are available (from child_process errors)
        if ('stdout' in err) {
          // @ts-ignore - We're checking if the property exists
          errorDetailsObj.stdout = err.stdout || '';
        }

        if ('stderr' in err) {
          // @ts-ignore - We're checking if the property exists
          errorDetailsObj.stderr = err.stderr || '';
        }

        // For child_process errors, also include the command and code
        if ('cmd' in err) {
          // @ts-ignore - We're checking if the property exists
          errorDetailsObj.cmd = err.cmd;
        }

        if ('code' in err) {
          // @ts-ignore - We're checking if the property exists
          errorDetailsObj.code = err.code;
        }
      } else {
        errorDetailsObj.message = String(err);
      }

      console.error('[APK Repacker] Error details:', JSON.stringify(errorDetailsObj, null, 2));

      return {
        success: false,
        error: errorMessage,
        details: errorDetailsObj.message
      };
    }
  } catch (err) {
    console.error('Error repacking APK:', err);
    const errorMessage = 'Error repacking APK';
    const errorDetails = err instanceof Error ? err.message : String(err);

    return {
      success: false,
      error: errorMessage,
      details: errorDetails || 'Unknown error occurred'
    };
  }
}

// Export the service functions
export const ApkRepackerService = {
  TMP_DIR,
  cleanupTempFiles,
  cleanupCompletedJobs,
  forceCleanupAllExceptRecent,
  validateApkFile,
  createRepackingJob
};

export default ApkRepackerService;
