import crypto from 'crypto';
import { env } from '$env/dynamic/private';

// Session configuration
const SESSION_SECRET = env.SESSION_SECRET || 'change-me-in-production-please';
const SESSION_EXPIRY = 60 * 60 * 24; // 24 hours in seconds

// In-memory session store (replace with a database in production)
const sessions = new Map();

/**
 * Create a new session
 * @param {Object} data - Data to store in the session
 * @returns {string} - Session ID
 */
export function createSession(data) {
  // Generate a secure random session ID
  const sessionId = crypto.randomBytes(32).toString('hex');

  // Create session with expiry
  const session = {
    id: sessionId,
    data,
    expires: Date.now() + (SESSION_EXPIRY * 1000)
  };

  // Store session
  sessions.set(sessionId, session);

  return sessionId;
}

/**
 * Get session data from session ID
 * @param {string} sessionId - Session ID
 * @returns {Object|null} - Session data or null if invalid/expired
 */
export function getSession(sessionId) {
  if (!sessionId) return null;

  const session = sessions.get(sessionId);

  // Check if session exists and is not expired
  if (!session || session.expires < Date.now()) {
    if (session) {
      // Clean up expired session
      sessions.delete(sessionId);
    }
    return null;
  }

  return session.data;
}

/**
 * Delete a session
 * @param {string} sessionId - Session ID to delete
 */
export function deleteSession(sessionId) {
  if (sessionId) {
    sessions.delete(sessionId);
  }
}

/**
 * Create a signed session cookie value
 * @param {string} sessionId - Session ID
 * @returns {string} - Signed session value
 */
export function signSession(sessionId) {
  const hmac = crypto.createHmac('sha256', SESSION_SECRET);
  hmac.update(sessionId);
  const signature = hmac.digest('hex');
  return `${sessionId}.${signature}`;
}

/**
 * Verify and extract session ID from signed session value
 * @param {string|undefined} signedSession - Signed session value
 * @returns {string|null} - Session ID if valid, null otherwise
 */
export function verifySession(signedSession) {
  if (!signedSession || !signedSession.includes('.')) {
    return null;
  }

  const [sessionId, signature] = signedSession.split('.');

  const hmac = crypto.createHmac('sha256', SESSION_SECRET);
  hmac.update(sessionId);
  const expectedSignature = hmac.digest('hex');

  try {
    if (crypto.timingSafeEqual(Buffer.from(signature), Buffer.from(expectedSignature))) {
      return sessionId;
    }
  } catch (error) {
    return null;
  }

  return null;
}

/**
 * Clean up expired sessions (call periodically)
 */
export function cleanupSessions() {
  const now = Date.now();
  for (const [sessionId, session] of sessions.entries()) {
    if (session.expires < now) {
      sessions.delete(sessionId);
    }
  }
}

// Set up periodic cleanup
if (typeof setInterval !== 'undefined') {
  // Only run in server environment
  setInterval(cleanupSessions, 60 * 60 * 1000); // Clean up every hour
}
