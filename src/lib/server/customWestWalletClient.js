// Custom WestWallet client that bypasses the problematic error handling
import { createRequire } from 'module';

const require = createRequire(import.meta.url);
const axios = require('axios');
const crypto = require('crypto');

export class CustomWestWalletAPI {
  constructor(apiKey, secretKey, baseUrl = "https://api.westwallet.io") {
    this.baseUrl = baseUrl;
    this.apiKey = apiKey;
    this.secretKey = secretKey;
  }

  _makeHeaders(data) {
    const timestamp = Math.floor(Date.now() / 1000);
    let dataString;

    if (Object.keys(data).length === 0) {
      dataString = "";
    } else {
      dataString = JSON.stringify(data);
    }

    const message = `${timestamp}${dataString}`;
    const signature = crypto
      .createHmac('sha256', this.secretKey)
      .update(message)
      .digest('hex');

    return {
      'Content-Type': 'application/json',
      'X-API-KEY': this.api<PERSON>ey,
      'X-ACCESS-SIGN': signature,
      'X-ACCESS-TIMESTAMP': timestamp,
      'Accept': 'application/json'
    };
  }

  async _makePostRequest(methodUrl, data) {
    try {
      const response = await axios({
        url: `${this.baseUrl}${methodUrl}`,
        method: 'POST',
        data: JSON.stringify(data),
        headers: this._makeHeaders(data)
      });

      // Check for API errors in response
      if (response.data && response.data.error && response.data.error !== "ok") {
        throw new Error(`WestWallet API Error: ${response.data.error}`);
      }

      return response.data;
    } catch (error) {
      if (error.response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        const errorMsg = error.response.data?.error || error.response.statusText || 'Unknown API error';
        throw new Error(`WestWallet API Error (${error.response.status}): ${errorMsg}`);
      } else if (error.request) {
        // The request was made but no response was received
        throw new Error('WestWallet API Error: No response received from server');
      } else {
        // Something happened in setting up the request that triggered an Error
        throw new Error(`WestWallet API Error: ${error.message}`);
      }
    }
  }

  async generateAddress(currency, ipnUrl = "", label = "") {
    const methodUrl = "/address/generate";
    const data = {
      currency: currency,
      ipn_url: ipnUrl,
      label: label
    };

    console.log(`Making WestWallet API request to ${this.baseUrl}${methodUrl} with data:`, data);
    
    try {
      const result = await this._makePostRequest(methodUrl, data);
      console.log(`WestWallet API response:`, result);
      return result;
    } catch (error) {
      console.error(`WestWallet API request failed:`, error);
      throw error;
    }
  }

  async walletBalance(currency) {
    const methodUrl = "/wallet/balance";
    const data = { currency: currency };
    return this._makePostRequest(methodUrl, data);
  }

  async walletBalances() {
    const methodUrl = "/wallet/balances";
    return this._makePostRequest(methodUrl, {});
  }
}
