<script>
  import * as ApkRepackerComponentModule from "$lib/components/ApkRepackerComponent.svelte";
  const ApkRepackerComponent = ApkRepackerComponentModule.default || ApkRepackerComponentModule;
  import { onMount } from "svelte";
  import { browser } from '$app/environment';

  // Get the data from the server load function
  export let data;
  export let form;

  // Default configuration templates
  const defaultConfigChat = `homeserver_url=
username=
password=
`;

  const defaultConfigVpn = `[Interface]
PrivateKey =
Address =
DNS = *******,*******

[Peer]
PublicKey =
PresharedKey =
Endpoint =
AllowedIPs = 0.0.0.0/0,::/0
`;

  // Default config for Phantom APK
  const defaultConfigPhantom = `id=**********`;

  // Find template paths for different APK types
  let chatTemplatePath = "";
  let vpnTemplatePath = "";
  let phantomTemplatePath = "";
  let otherTemplatePath = "";

  // References to component instances
  /**
   * @typedef {Object} ApkRepackerComponentInstance
   * @property {Function} isReadyForSubmission - Check if component is ready for submission
   * @property {Function} triggerSubmit - Trigger form submission
   * @property {Function} getDownloadInfo - Get download info
   * @property {Function} getTitle - Get component title
   * @property {string} [id] - Component ID
   */

  /** @type {any} */
  let chatComponent = null;
  /** @type {any} */
  let vpnComponent = null;
  /** @type {any} */
  let phantomComponent = null;
  /** @type {any} */
  let otherComponent = null;

  // State for batch operations
  let processingAll = false;
  let allDownloadsReady = false;
  let forceShowDownloadButton = false;
  /** @type {Array<{url: string, filename: string, isReady: boolean, title: string, jobId?: string | null}>} */
  let downloadLinks = [];
  /** @type {Array<any>} */
  let componentsWithMethods = [];
  // Track the total number of components
  let totalComponentCount = 0;

  // Intervals for periodic checks
  /**
   * @type {number | null | undefined}
   */
  let checkStatusInterval = null;
  /**
   * @type {number | null | undefined}
   */
  let fallbackCheckInterval = null;

  // Create a function to handle download links updates
  // This approach is more reliable in production builds than reactive statements
  function updateDownloadLinksState() {
    if (downloadLinks.length > 0) {
      // Always force the UI to update when download links change
      allDownloadsReady = true;
      forceShowDownloadButton = true;
    }
  }

  // Create reactive statements to update the UI when state changes
  // This ensures our update function is called whenever downloadLinks changes
  $: if (downloadLinks) updateDownloadLinksState();

  // Create a reactive statement to update the UI when a new result comes in
  $: {
    if (form) {
      if (form.success && form.jobId && form.apkName) {
        // Explicitly call our update function immediately
        updateDownloadLinksState();

        setTimeout(() => {
          checkAllDownloadsReady();
          // Call update function again after the check
          updateDownloadLinksState();
        }, 100);
      }
    }
  }

  // This onMount function is intentionally left empty as we've merged it with the one at the bottom of the file
  // to ensure all initialization code runs in a single onMount callback
  // onMount(() => { ... });

  // Function to initialize template paths
  function initializeTemplatePaths() {
    if (data.templates && data.templates.length > 0) {
      // Find templates by name pattern
      for (const template of data.templates) {
        if (template.filename.toLowerCase().includes('chat')) {
          chatTemplatePath = template.path;
        } else if (template.filename.toLowerCase().includes('vpn')) {
          vpnTemplatePath = template.path;
        } else if (template.filename.toLowerCase().includes('phantom')) {
          phantomTemplatePath = template.path;
          console.log('Found Phantom template at:', phantomTemplatePath);
        } else if (!otherTemplatePath) {
          // Use the first template that doesn't match other patterns as a fallback
          otherTemplatePath = template.path;
        }
      }

      // If specific templates weren't found, use the first available template
      if (!chatTemplatePath && data.templates.length > 0) {
        chatTemplatePath = data.templates[0].path;
      }
      if (!vpnTemplatePath && data.templates.length > 1) {
        vpnTemplatePath = data.templates[1].path;
      } else if (!vpnTemplatePath) {
        vpnTemplatePath = chatTemplatePath;
      }
      if (!phantomTemplatePath) {
        // Try to find by order if not found by name
        const phantomIndex = data.templates.findIndex(t => t.filename.toLowerCase().includes('phantom'));
        if (phantomIndex !== -1) {
          phantomTemplatePath = data.templates[phantomIndex].path;
          console.log('Found Phantom template at:', phantomTemplatePath);
        }
      }
    }
  }

  // Initialize template paths immediately
  initializeTemplatePaths();

  // Handle form result events from components
  /**
   * @param {CustomEvent<any>} event - The result event from the component
   */
  function handleResult(event) {
    const result = event.detail;

    // Update the form state
    form = result;

    // Check if the result was successful
    if (result.success) {
      // DIRECT APPROACH: If we have a successful result with a jobId and apkName,
      // immediately add it to the download links
      if (result.jobId && result.apkName) {
        // Extract the timestamp part from the jobId
        const timestampMatch = result.jobId.match(/^(\d+)/);
        const timestamp = timestampMatch ? timestampMatch[1] : result.jobId;

        // Create the download URL
        const url = `/admin/apk/download/${timestamp}/${result.apkName}`;

        // Get the component title
        let title = result.componentId || 'Unknown APK';

        // Try to get a better title from the component ID
        if (result.componentId) {
          if (result.componentId.includes('chat')) {
            title = 'Messenger APK';
          } else if (result.componentId.includes('vpn')) {
            title = 'VPN APK';
          } else {
            title = 'Other APK';
          }
        }

        // Create a new download link object
        const newDownloadLink = {
          url,
          filename: result.apkName,
          title,
          jobId: result.jobId,
          isReady: true
        };

        // Check if this download link already exists
        const existingIndex = downloadLinks.findIndex(link =>
          link.jobId === result.jobId || link.filename === result.apkName
        );

        if (existingIndex >= 0) {
          // Update the existing link
          const updatedLinks = [...downloadLinks];
          updatedLinks[existingIndex] = newDownloadLink;
          downloadLinks = updatedLinks;
        } else {
          // Add the new link
          downloadLinks = [...downloadLinks, newDownloadLink];
        }

        // Force the UI to update
        allDownloadsReady = true;
        forceShowDownloadButton = true;

        // Explicitly call our update function to ensure it runs in production builds
        updateDownloadLinksState();
      }

      // Still run the normal check to catch any other components
      setTimeout(() => {
        // Check if all downloads are ready after each successful result
        checkAllDownloadsReady();

        // Schedule multiple additional checks with increasing delays
        // This helps ensure we catch all APKs as they become ready
        setTimeout(() => {
          checkAllDownloadsReady();
        }, 1000);

        setTimeout(() => {
          checkAllDownloadsReady();
        }, 2000);

        setTimeout(() => {
          checkAllDownloadsReady();
        }, 3000);
      }, 500);
    }
  }

  // Function to repack all APKs
  async function repackAll() {
    if (processingAll) return;

    processingAll = true;
    allDownloadsReady = false;
    downloadLinks = [];

    // Create an array of components that exist
    const components = [
      chatComponent,
      vpnComponent,
      otherComponent
    ].filter(component => component !== null);

    // Check if we have any components
    if (components.length === 0) {
      processingAll = false;
      return;
    }

    // Check if all components have the required methods
    const allHaveMethods = components.every(component =>
      typeof component.isReadyForSubmission === 'function' &&
      typeof component.triggerSubmit === 'function' &&
      typeof component.getDownloadInfo === 'function' &&
      typeof component.getTitle === 'function'
    );

    if (!allHaveMethods) {
      processingAll = false;
      return;
    }

    // Check if any component is not ready
    const allReady = components.every(component => component.isReadyForSubmission());

    if (!allReady) {
      processingAll = false;
      return;
    }

    // Trigger submission for each component with a delay
    let submissionPromises = [];

    try {
      for (let i = 0; i < components.length; i++) {
        const component = components[i];
        const componentTitle = typeof component.getTitle === 'function' ?
          component.getTitle() : `Component ${i+1}`;
        const delay = i * 2000; // 2 second delay between submissions - enough to prevent race conditions

        const submissionPromise = new Promise(resolve => {
          setTimeout(() => {
            try {
              const success = component.triggerSubmit();
              resolve(success);
            } catch (error) {
              console.error(`Error triggering submission for ${componentTitle}:`, error);
              resolve(false);
            }
          }, delay);
        });

        submissionPromises.push(submissionPromise);
      }

      // Wait for all submissions to complete
      const results = await Promise.all(submissionPromises);

      // Check if any submissions failed
      const allSuccessful = results.every(result => result === true);
      if (!allSuccessful) {
        console.warn('Some APK repacking operations may not have been triggered successfully');
      }

      // Set up a periodic check for download status
      let checkCount = 0;
      const maxChecks = 10; // Maximum number of checks (10 checks * 1 second = 10 seconds max wait)

      const checkInterval = setInterval(() => {
        checkCount++;

        // Check if all downloads are ready
        checkAllDownloadsReady();

        // If all downloads are ready or we've reached the maximum number of checks, clear the interval
        if (allDownloadsReady || checkCount >= maxChecks) {
          clearInterval(checkInterval);
          processingAll = false;
        }
      }, 1000); // Check every 1 second
    } catch (error) {
      console.error('Error during batch submission:', error);
      alert('An error occurred while repacking APKs. Please try again.');
      processingAll = false;
    }
  }

  // Function to check if all downloads are ready
  function checkAllDownloadsReady() {
    // Create an array of components that exist
    const components = [
      chatComponent,
      vpnComponent,
      otherComponent
    ].filter(component => component !== null);

    // If no components exist, we can't have any downloads ready
    if (components.length === 0) {
      allDownloadsReady = false;
      downloadLinks = [];
      return;
    }

    // Store the total number of components for reference
    totalComponentCount = components.length;

    // Check if all components have the required methods
    // In production builds, we need to be more careful about checking methods
    componentsWithMethods = [];
    for (const component of components) {
      // Check each method individually
      // @ts-ignore - We know these methods exist at runtime
      const hasGetDownloadInfo = typeof component.getDownloadInfo === 'function';
      // @ts-ignore - We know these methods exist at runtime
      const hasGetTitle = typeof component.getTitle === 'function';

      if (hasGetDownloadInfo && hasGetTitle) {
        componentsWithMethods.push(component);
      }
    }

    // If we have no valid components, we can't proceed
    if (componentsWithMethods.length === 0) {
      allDownloadsReady = false;
      downloadLinks = [];
      return;
    }

    // Get download info from each component
    const downloadInfos = [];

    for (const component of componentsWithMethods) {
      try {
        // @ts-ignore - We know these methods exist at runtime
        const info = component.getDownloadInfo();
        // @ts-ignore - We know these methods exist at runtime
        const title = typeof component.getTitle === 'function' ? component.getTitle() : 'Unknown';

        // Skip components with invalid download info
        if (!info) {
          console.warn(`Component ${title} returned invalid download info`);
          continue;
        }

        // Ensure the URL is correct
        let url = info.url || '';
        let jobId = info.jobId || null;
        let filename = info.filename || '';
        let isReady = !!info.isReady;

        // Check if URL is valid
        if (url && url.startsWith('/admin/apk/download/')) {
          // URL starts with the correct prefix but might still have the wrong format
          const match = url.match(/\/admin\/apk\/download\/([^\/]+)\/([^\/]+)/);
          if (match && match.length >= 3) {
            const jobIdPart = match[1];
            filename = match[2];

            // Check if jobId contains a dash (indicating it's the full component ID)
            if (jobIdPart.includes('-')) {
              // Extract just the timestamp part
              const timestampMatch = jobIdPart.match(/^(\d+)/);
              if (timestampMatch) {
                const timestamp = timestampMatch[1];
                url = `/admin/apk/download/${timestamp}/${filename}`;
              }
            }

            // Extract jobId from URL if not already available
            if (!jobId) {
              const timestampMatch = jobIdPart.match(/^(\d+)/);
              jobId = jobIdPart.includes('-') ?
                (timestampMatch ? timestampMatch[1] : jobIdPart) :
                jobIdPart;
            }
          }
        } else if (jobId && filename) {
          // Extract just the timestamp part from the jobId (e.g., "1746921113548-apk-repacker-v0jkk7z" -> "1746921113548")
          const timestampMatch = jobId.match(/^(\d+)/);
          const timestamp = timestampMatch ? timestampMatch[1] : jobId;

          url = `/admin/apk/download/${timestamp}/${filename}`;
        } else if (filename) {
          // Try to extract jobId from the filename if it follows the pattern: name-timestamp.apk
          const filenameMatch = filename.match(/^(.+)-(\d{4,})\.apk$/);
          if (filenameMatch && filenameMatch.length >= 3) {
            const extractedTimestamp = filenameMatch[2];
            jobId = extractedTimestamp;
            url = `/admin/apk/download/${extractedTimestamp}/${filename}`;
          } else {
            console.warn(`Cannot correct download URL for ${title}: No jobId available and filename doesn't contain timestamp`);
          }
        }

        // Only add valid download info
        if (url && filename) {
          downloadInfos.push({
            url,
            filename,
            title,
            jobId,
            isReady
          });
        }
      } catch (error) {
        console.error(`Error getting download info from component:`, error);
      }
    }

    // Check if all components have download URLs
    const readyComponents = downloadInfos.filter(info => info.isReady);

    // For production builds, check if we have any ready components at all
    const anyComponentsReady = readyComponents.length > 0;

    // Update the allDownloadsReady flag - in any environment, show the button if ANY components are ready
    // This is the key change - we'll always show the button if any APKs are ready
    allDownloadsReady = anyComponentsReady;

    // In production, always force show the button if we have any ready components
    if (process.env.NODE_ENV === 'production' && readyComponents.length > 0) {
      forceShowDownloadButton = true;
    }

    // Update download links - create a new array to ensure reactivity
    if (readyComponents.length > 0) {
      // Create a new array to ensure Svelte detects the change
      const newLinks = [...readyComponents];

      // Always create a completely new array to ensure reactivity
      downloadLinks = [...newLinks];

      // Explicitly call our update function to ensure it runs in production builds
      updateDownloadLinksState();

      // Force the UI to update by toggling the allDownloadsReady flag
      allDownloadsReady = false;
      setTimeout(() => {
        allDownloadsReady = true;
        // Call the update function again after the toggle to ensure UI updates
        updateDownloadLinksState();
      }, 10);
    } else {
      downloadLinks = [];
    }
  }

  // Function to download all APKs
  async function downloadAll() {
    // Force a final check for download links
    checkAllDownloadsReady();

    // Use the downloadLinks array that's already populated
    if (downloadLinks.length === 0) {
      console.error("No APKs are ready for download");
      return;
    }

    // Trigger downloads for each ready APK with a small delay between each
    let downloadCount = 0;

    // Use a function to download each APK with a delay
    /**
     * @param {number} index - The index of the APK to download
     */
    async function downloadWithDelay(index) {
      if (index >= downloadLinks.length) {
        return;
      }

      const info = downloadLinks[index];

      if (info.isReady) {
        // Ensure the URL is correct
        let downloadUrl = info.url;

        // Check if URL is valid
        if (!downloadUrl || !downloadUrl.startsWith('/admin/apk/download/')) {
          // Try to fix the URL using jobId if available
          if (info.jobId) {
            // Extract just the timestamp part from the jobId (e.g., "1746921113548-apk-repacker-v0jkk7z" -> "1746921113548")
            const timestampMatch = info.jobId.match(/^(\d+)/);
            const timestamp = timestampMatch ? timestampMatch[1] : info.jobId;

            downloadUrl = `/admin/apk/download/${timestamp}/${info.filename}`;
          } else {
            // Try to extract jobId from the filename if it follows the pattern: name-timestamp.apk
            const filenameMatch = info.filename.match(/^(.+)-(\d{4,})\.apk$/);
            if (filenameMatch && filenameMatch.length >= 3) {
              const extractedTimestamp = filenameMatch[2];
              downloadUrl = `/admin/apk/download/${extractedTimestamp}/${info.filename}`;
            } else {
              console.error(`Cannot fix download URL for ${info.title}: No jobId available and filename doesn't contain timestamp`);
              // Skip this download and move to the next one
              setTimeout(() => downloadWithDelay(index + 1), 100);
              return;
            }
          }
        } else {
          // URL starts with the correct prefix but might still have the wrong format
          // Extract the jobId part and check if it contains a dash
          const urlMatch = downloadUrl.match(/\/admin\/apk\/download\/([^\/]+)\/([^\/]+)/);
          if (urlMatch && urlMatch.length >= 2) {
            const jobIdPart = urlMatch[1];
            if (jobIdPart.includes('-')) {
              // Extract just the timestamp part
              const timestampMatch = jobIdPart.match(/^(\d+)/);
              if (timestampMatch) {
                const timestamp = timestampMatch[1];
                downloadUrl = `/admin/apk/download/${timestamp}/${info.filename}`;
              }
            }
          }
        }

        try {
          // Use fetch to get the file
          const response = await fetch(downloadUrl);

          if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
          }

          // Get the file as a blob
          const blob = await response.blob();

          // Create a URL for the blob
          const url = window.URL.createObjectURL(blob);

          // Create a link and click it to download
          const link = document.createElement('a');
          link.style.display = 'none';
          link.href = url;
          link.download = info.filename;
          document.body.appendChild(link);
          link.click();

          // Clean up
          window.URL.revokeObjectURL(url);
          document.body.removeChild(link);

          downloadCount++;
        } catch (error) {
          console.error(`Error downloading ${info.title}:`, error);
          const errorMessage = error instanceof Error ? error.message : String(error);
          alert(`Error downloading ${info.title}: ${errorMessage}. Please try again.`);
        }

        // Download the next APK after a minimal delay
        setTimeout(() => downloadWithDelay(index + 1), 100);
      } else {
        // Skip this APK and move to the next one
        downloadWithDelay(index + 1);
      }
    }

    // Start the download process
    downloadWithDelay(0);

    // Show a message to the user only when all downloads are complete
    setTimeout(() => {
      // if (downloadCount === downloadLinks.length) {
      //   alert(`All ${downloadCount} APKs have been downloaded successfully.`);
      // }
    }, downloadLinks.length * 200); // Wait a bit for all downloads to complete
  }

  // Use a manual initialization approach
  let initialized = false;

  // Function to initialize the component
  function initialize() {
    if (initialized) return;
    initialized = true;

    // Make sure template paths are initialized
    initializeTemplatePaths();

    // Explicitly call our update function on initialization
    updateDownloadLinksState();

    // Initial check for download status
    checkAllDownloadsReady();

    // Set up periodic checks for download status
    if (browser) {
      // Set up a periodic check every 2 seconds
      checkStatusInterval = window.setInterval(() => {
        checkAllDownloadsReady();

        // If all downloads are ready, clear the interval
        if (allDownloadsReady && downloadLinks.length === totalComponentCount) {
          cleanup();
        }
      }, 2000);

      // Set up production environment fallbacks
      if (process.env.NODE_ENV === 'production') {
        // Listen for successful form submissions
        const originalFetch = window.fetch;
        window.fetch = async function(...args) {
          const result = await originalFetch.apply(this, args);

          // Check if this was a form submission to the APK repacker endpoint
          if (args[0] && typeof args[0] === 'string' &&
              args[0].includes('repackApk') &&
              args[1] && args[1].method === 'POST') {

            // Wait for the response to be processed
            setTimeout(() => {
              // Check if we have any download links but the button isn't showing
              if (downloadLinks.length > 0 && !allDownloadsReady) {
                forceShowDownloadButton = true;
              }
            }, 5000);
          }

          return result;
        };
      }

      // Set up fallback check interval (every 10 seconds)
      fallbackCheckInterval = window.setInterval(() => {
        // Force a check for download links
        checkAllDownloadsReady();

        // In production, force the download button to show if we have any links
        if (process.env.NODE_ENV === 'production' && downloadLinks.length > 0) {
          forceShowDownloadButton = true;
        }
      }, 10000);

      // Listen for form submission events which might indicate a successful repacking
      window.addEventListener('submit', handleFormSubmission);

      // Check again when the page becomes visible (user returns to tab)
      document.addEventListener('visibilitychange', handleVisibilityChange);

      // Add click listeners to all "Repack APK" buttons
      setTimeout(addRepackButtonListeners, 1000);
    }
  }

  // Function to handle form submissions
  /**
   * @param {Event} event - The form submission event
   */
  function handleFormSubmission(event) {
    // Check if this is a form submission for APK repacking
    const form = event.target;
    // @ts-ignore - We know this is a form element
    if (form && form.tagName === 'FORM' && form.action && form.action.includes('repackApk')) {
      // Schedule multiple checks with increasing delays
      setTimeout(checkAllDownloadsReady, 3000);
      setTimeout(checkAllDownloadsReady, 5000);

      // In production, force the download button to show after a longer delay
      if (process.env.NODE_ENV === 'production') {
        setTimeout(() => {
          if (downloadLinks.length > 0) {
            forceShowDownloadButton = true;
          }
        }, 8000);
      }
    }
  }

  // Function to handle visibility changes
  function handleVisibilityChange() {
    if (!document.hidden) {
      checkAllDownloadsReady();
    }
  }

  // Function to add click listeners to repack buttons
  function addRepackButtonListeners() {
    const repackButtons = document.querySelectorAll('button[type="submit"]');
    repackButtons.forEach(button => {
      button.addEventListener('click', () => {
        // Schedule multiple checks with increasing delays
        setTimeout(checkAllDownloadsReady, 3000);
        setTimeout(checkAllDownloadsReady, 6000);

        // In production, force the download button to show after a longer delay
        if (process.env.NODE_ENV === 'production') {
          setTimeout(() => {
            if (downloadLinks.length > 0 && !allDownloadsReady) {
              forceShowDownloadButton = true;
            }
          }, 10000);
        }
      });
    });
  }

  // Function to clean up resources
  function cleanup() {
    if (checkStatusInterval) {
      window.clearInterval(checkStatusInterval);
      checkStatusInterval = null;
    }

    if (fallbackCheckInterval) {
      window.clearInterval(fallbackCheckInterval);
      fallbackCheckInterval = null;
    }

    // Remove event listeners
    if (browser) {
      window.removeEventListener('submit', handleFormSubmission);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    }
  }

  // Initialize on mount
  onMount(() => {
    initialize();

    // Cleanup when component is unmounted
    return cleanup;
  });

  // Also initialize when the component is first rendered
  if (browser) {
    initialize();
  }
</script>

<svelte:head>
  <title>Phantom - APK Repacker</title>
</svelte:head>

<!-- APK Repacker Card -->
<div class="admin-card">
  <div class="admin-card-header">
    <div class="admin-card-title">APK Repacker</div>

    <!-- Batch Action Buttons -->
    {#if data.templates && data.templates.length > 0}
      <div class="batch-actions">
        {#if (allDownloadsReady && downloadLinks.length > 0) || forceShowDownloadButton}
          <div class="download-ready-container">
            <div class="download-ready-indicator">
              <span class="download-ready-dot"></span>
              <span class="download-ready-text">
                {#if downloadLinks.length === 1}
                  1 APK Ready
                {:else if downloadLinks.length === totalComponentCount}
                  All APKs Ready ({downloadLinks.length})
                {:else}
                  {downloadLinks.length} of {totalComponentCount} APKs Ready
                {/if}
              </span>
            </div>
            <button
              type="button"
              class="button download-all-button"
              on:click={downloadAll}
            >
              {#if downloadLinks.length === 1}
                Download APK
              {:else if downloadLinks.length === totalComponentCount}
                Download All APKs
              {:else}
                Download {downloadLinks.length} Ready APKs
              {/if}
            </button>
          </div>
        {:else}
          <button
            type="button"
            class="button repack-all-button"
            on:click={repackAll}
            disabled={processingAll}
          >
            {#if processingAll}
              Processing...
            {:else}
              Repack All APKs
            {/if}
          </button>
        {/if}
      </div>
    {/if}
  </div>

  <div class="admin-card-description">
    Configure and repack APK files with custom settings.
  </div>

  <div class="apk-repacker-container">
    <!-- No templates message -->
    {#if !data.templates || data.templates.length === 0}
      <div class="no-templates-message">
        No APK templates found. Please add template files to the apk-templates directory.
      </div>
    {:else}
      <!-- Messenger APK Repacker -->
      {#if chatTemplatePath}
        <ApkRepackerComponent
          bind:this={chatComponent}
          title="Messenger APK"
          templatePath={chatTemplatePath}
          defaultConfig={defaultConfigChat}
          configFilename="credentials.txt"
          csrfToken={data.csrfToken}
          submissionDelay={0}
          on:result={handleResult}
        />
      {/if}

      <!-- VPN APK Repacker -->
      {#if vpnTemplatePath}
        <ApkRepackerComponent
          bind:this={vpnComponent}
          title="VPN APK"
          templatePath={vpnTemplatePath}
          defaultConfig={defaultConfigVpn}
          configFilename="vpn.conf"
          csrfToken={data.csrfToken}
          submissionDelay={250}
          on:result={handleResult}
        />
      {/if}

      <!-- Phantom APK Repacker -->
      {#if phantomTemplatePath}
        <ApkRepackerComponent
          bind:this={phantomComponent}
          title="Phantom APK"
          templatePath={phantomTemplatePath}
          defaultConfig={defaultConfigPhantom}
          configFilename="customer.conf"
          csrfToken={data.csrfToken}
          submissionDelay={400}
          on:result={handleResult}
        />
      {/if}

      <!-- Other APK Repacker (if there's a third template) -->
      {#if otherTemplatePath && otherTemplatePath !== chatTemplatePath && otherTemplatePath !== vpnTemplatePath && otherTemplatePath !== phantomTemplatePath}
        <ApkRepackerComponent
          bind:this={otherComponent}
          title="Other APK"
          templatePath={otherTemplatePath}
          defaultConfig={defaultConfigVpn}
          configFilename="config.txt"
          csrfToken={data.csrfToken}
          submissionDelay={500}
          on:result={handleResult}
        />
      {/if}
    {/if}
  </div>
</div>