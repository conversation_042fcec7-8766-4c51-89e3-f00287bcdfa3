import { fail, redirect } from '@sveltejs/kit';
import { supabase } from '$lib/server/supabase.js';

/** @type {import('./$types').PageServerLoad} */
export async function load() {
    try {
        // Load all devices with team information for the dropdown
        const { data: devices, error } = await supabase
            .from('devices')
            .select(`
                internal_id,
                ip,
                nickname,
                team_id,
                fcm_token,
                teams!devices_team_id_fkey (
                    id,
                    balance
                )
            `)
            .order('team_id', { ascending: true })
            .order('nickname', { ascending: true });

        if (error) {
            console.error('Error loading devices:', error);
            return {
                devices: [],
                error: 'Failed to load devices'
            };
        }

        // Format devices for dropdown with IP first
        const formattedDevices = devices.map(device => ({
            internal_id: device.internal_id,
            ip: device.ip,
            nickname: device.nickname,
            team_id: device.team_id,
            has_fcm_token: !!device.fcm_token,
            display_name: `${device.ip || 'Unknown IP'}${device.nickname ? ` (${device.nickname})` : ''} - ${device.team_id}`,
            team_info: device.teams
        }));

        return {
            devices: formattedDevices
        };

    } catch (err) {
        console.error('Error in load function:', err);
        return {
            devices: [],
            error: 'Failed to load page data'
        };
    }
}

/** @type {import('./$types').Actions} */
export const actions = {
    sendNotification: async ({ request, locals, fetch, cookies }) => {
        const form = await request.formData();
        const title = form.get('title');
        const message = form.get('message');
        const selectedDevice = form.get('selectedDevice'); // This will be device internal_id

        console.log('[sendNotification] Form data:', { title, message, selectedDevice });

        const authToken = process.env.WEBVIEW_AUTH_TOKEN || '01QIm8aW';
        if (!authToken) {
            console.log('No auth token found');
            return fail(401, { error: 'Unauthorized: No auth token found' });
        }

        try {
            if (!selectedDevice) {
                return fail(400, { error: 'Please select a device' });
            }

            // Get device info to determine team_id automatically and send to specific device
            const { data: device, error } = await supabase
                .from('devices')
                .select('team_id, ip, nickname, fcm_token')
                .eq('internal_id', selectedDevice)
                .single();

            if (error || !device) {
                return fail(400, { error: 'Selected device not found' });
            }

            if (!device.fcm_token) {
                return fail(400, { error: `Device ${device.nickname || device.ip} does not have an FCM token` });
            }

            const teamId = device.team_id;
            const deviceId = device.ip; // Use IP as device identifier for notifications

            console.log('[sendNotification] Sending to specific device:', deviceId, 'in team:', teamId);

            const notificationOptions = {
                title: String(title),
                message: String(message),
                teamId,
                deviceId
            };

            const result = await import('$lib/server/notifications').then(mod =>
                mod.sendNotification(notificationOptions)
            );

            console.log('[sendNotification] Success:', result);
            return {
                success: true,
                response: result?.summary || result?.response || 'Notification sent',
                details: result,
                sentTo: `Device ${deviceId} in team ${teamId}`
            };
        } catch (err) {
            console.error('[sendNotification] Error:', err);
            const errorMessage = err instanceof Error ? err.message : 'Failed to send notification';
            return fail(500, { error: errorMessage });
        }
    }
};
