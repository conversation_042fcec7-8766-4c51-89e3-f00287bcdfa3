import { supabase } from '$lib/server/supabase';
import { v4 as uuidv4 } from 'uuid';

export async function load() {
  try {
    // Get device count and last connected device
    const { count: totalDevices = 0, error: countError } = await supabase
      .from('devices')
      .select('*', { count: 'exact', head: true });

    if (countError) {
      console.error('Error counting devices:', countError);
      throw new Error('Failed to count devices');
    }

    // Get all devices with team info first
    const { data: allDevicesData, error: allDevicesError } = await supabase
      .from('devices')
      .select(`
        *,
        teams!devices_team_id_fkey (
          id,
          balance
        )
      `)
      .order('created_at', { ascending: false });

    if (allDevicesError) {
      console.error('Error fetching devices:', { allDevicesError });
      throw new Error('Failed to load device data');
    }

    // Only try to get recent device if we have devices
    let recentDevice = null;
    if (allDevicesData && allDevicesData.length > 0) {
      // Use the first device from the already fetched data as the most recent
      recentDevice = allDevicesData[0];
    }

    return {
      devices: allDevicesData || [],
      totalDevices,
      recentDevice: recentDevice || null,
      error: null
    };
  } catch (error) {
    console.error('Error in devices page load:', error);
    return { 
      devices: [], 
      totalDevices: 0,
      recentDevice: null,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    };
  }
}

export const actions = {
  delete: async ({ request }) => {
    const formData = await request.formData();
    const id = formData.get('id');

    if (!id || typeof id !== 'string') {
      return { success: false, error: 'Missing or invalid device ID' };
    }

    const { error } = await supabase
      .from('devices')
      .delete()
      .eq('internal_id', id);

    if (error) {
      console.error('Error deleting device:', error);
      return { success: false, error: error.message };
    }

    return { success: true };
  }
};
