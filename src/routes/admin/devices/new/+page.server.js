import { fail, redirect } from "@sveltejs/kit";
import { supabase } from "$lib/server/supabase";
import { v4 as uuidv4 } from "uuid";

export const load = async () => {
  // Fetch all teams for the dropdown
  const { data: teams, error } = await supabase
    .from('teams')
    .select('id, internal_id')
    .order('id', { ascending: true });

  if (error) {
    console.error('Error fetching teams:', error);
    return { teams: [] };
  }

  return { teams };
};

export const actions = {
  default: async ({ request }) => {
    const formData = await request.formData();
    const teamId = formData.get("team_id");

    // Validate required fields
    if (!teamId) {
      return fail(400, { success: false, error: "Team ID is required" });
    }

    // Get team internal_id
    const { data: team, error: teamError } = await supabase
      .from("teams")
      .select("internal_id")
      .eq("id", teamId)
      .single();

    if (teamError || !team) {
      return fail(400, { success: false, error: "Team not found" });
    }

    const ip = formData.get("ip");
    if (typeof ip !== "string" || !ip.trim()) {
      return fail(400, { success: false, error: "IP address is required" });
    }

    // Prepare device data
    const now = new Date().toISOString();
    const device = {
      internal_id: uuidv4(),
      team_internal_id: team.internal_id,
      team_id: teamId.toString(),
      ip: formData.get("ip") || null,
      nickname: formData.get("nickname") || null,
      vpn_conf: formData.get("vpn_conf") || null,
      msg_conf: formData.get("msg_conf") || null,
      phone_conf: formData.get("phone_conf") || null,
      role: formData.get("role") || null,
      created_at: now,
      last_auth_at: null,
    };

    // Insert the new device
    const { data, error: dbError } = await supabase
      .from("devices")
      .insert([device])
      .select()
      .single();

    if (dbError) {
      console.error("Error creating device:", dbError);
      return fail(500, { success: false, error: dbError.message });
    }

    // Redirect to the devices list page
    throw redirect(303, "/admin/devices");
  },
};
