// This ensures consistent server-side behavior across admin routes
import { redirect } from '@sveltejs/kit';
import { getSession, verifySession } from '$lib/server/session';
import { generateCsrfToken } from '$lib/server/csrf';

/** @type {import('./$types').LayoutServerLoad} */
export async function load({ cookies, url }) {
  // Get the signed session from the cookie
  const signedSession = cookies.get('admin_session');

  // Verify the session signature and get the session ID
  /** @type {string|null} */
  const sessionId = verifySession(signedSession);

  // Get the session data if the session ID is valid
  const session = sessionId ? getSession(sessionId) : null;

  // Check if user is authenticated
  const isAuthenticated = !!session;

  // If not authenticated, redirect to login page only if not already there
  if (!isAuthenticated && !url.pathname.endsWith('/admin') && !url.searchParams.has('login')) {
    throw redirect(303, '/admin?login=true');
  }

  // Generate CSRF token if authenticated
  const csrfToken = sessionId ? generateCsrfToken(sessionId) : '';

  // Return data for layout
  return {
    authenticated: isAuthenticated,
    csrfToken,
    form: null, // Will be populated by actions
    showMessage: false
  };
}

// No actions in layout server file - moved to page server file
