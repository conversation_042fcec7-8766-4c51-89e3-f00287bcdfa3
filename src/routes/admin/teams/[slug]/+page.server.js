import { json } from '@sveltejs/kit';
import { error, redirect } from '@sveltejs/kit';
import { supabase } from '$lib/server/supabase';
import { getTeamWallets } from '$lib/server/walletService.js';
import { deleteTeamWithAssociatedData, getTeamStatistics } from '$lib/server/teamService.js';

// SvelteKit page server for /team/new (no server data needed)
export const load = async ({ params }) => {
    const id = params.slug;
    // Fetch team data
    const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .select('*')
        .eq('id', id)
        .single();
        
    if (teamError) {
        console.error('Error fetching team:', teamError);
        throw error(404, 'Team not found');
    }
    
    // Fetch devices for this team
    const { data: devices, error: devicesError } = await supabase
        .from('devices')
        .select('*')
        .eq('team_id', id)
        .order('created_at', { ascending: false });

    if (devicesError) {
        console.error('Error fetching devices:', devicesError);
        // Don't throw error, just return empty devices array
    }

    // Fetch wallets for this team
    let wallets = [];
    try {
        wallets = await getTeamWallets(id);
    } catch (walletError) {
        console.error('Error fetching wallets:', walletError);
        // Don't throw error, just return empty wallets array
    }

    // Get team statistics for deletion confirmation
    const statistics = await getTeamStatistics(id);

    return {
        team: teamData,
        devices: devices || [],
        wallets: wallets || [],
        statistics
    };
};

// Add actions for PUT and DELETE 
export const actions = {
    delete: async ({ params }) => {
        const id = params.slug;

        const result = await deleteTeamWithAssociatedData(id);

        if (!result.success) {
            throw error(500, result.error || 'Failed to delete team');
        }

        throw redirect(303, '/admin/teams');
    },
    update: async ({ request, params }) => {
        const currentId = params.slug;
        const formData = await request.formData();
        /** @type {Record<string, any>} */
        const updateFields = {};

        // Extract fields from form data
        const newId = formData.get('id');
        const balance = formData.get('balance');
        const role = formData.get('role');
        // Add other fields if needed, ensure they are allowed update fields in your schema
        // ['created_at', 'next_charge_at', 'owner_internal_id', 'owner_id'] are usually not editable via this form

        if (newId !== null && newId !== currentId) {
            // If ID is being changed, add it to updateFields
            updateFields.id = newId;
        }

        if (balance !== null) {
             // Convert balance to number if it exists
             updateFields.balance = parseFloat(balance.toString());
             if (isNaN(updateFields.balance)) {
                 return { success: false, error: 'Invalid balance value' };
             }
        }

        if (role !== null) {
             // Only update role if a value is provided (handle the 'Select Role' option if it sends null/empty string)
             // Assuming 'Select Role' option sends an empty string or null, adjust if needed
             updateFields.role = role === '' ? null : role;
        }


        if (Object.keys(updateFields).length === 0) {
            return { success: false, error: 'No valid fields to update' };
        }
        const db = supabase; // Explicitly reference supabase
        // Use the currentId (from params) for the update query filter
        const { data, error: dbError } = await /** @type {any} */ (db.from('teams')).update(updateFields).eq('id', currentId).select().single();

        if (dbError) {
            console.error('Supabase update error:', dbError); // Log the actual error
            // Check for specific errors like unique constraint violation for ID
            if (dbError.code === '23505') { // PostgreSQL unique violation error code
                 return { success: false, error: `Customer ID "${newId}" already exists.` };
            }
            throw error(500, dbError.message || 'Failed to update team');
        }

        throw redirect(303, `/admin/teams`);
    }
}; 