<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { enhance } from '$app/forms';
  import { invalidateAll } from '$app/navigation';


  let initialized = false;

  type Team = {
    id: string;
    balance: number;
    next_charge_at: string | null;
    created_at?: string;
    internal_id?: string;
    owner_internal_id?: string | null;
    owner_id?: string | null;
  };

  export let data;
  $: ({ teams, error, totalTeams, totalBalance } = data);

  function openAddForm() {
    goto("/admin/teams/new");
  }

  function openEditForm(team: Team) {
    goto(`/admin/teams/${team.id}`);
  }


  function initialize() {
    if (initialized) return;
    initialized = true;
  }
  
  onMount(() => {
    initialize();
    // return cleanup; // Cleanup is no longer needed
  });

  function formatDate(dateString: string | undefined) {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      // Format date without seconds
      return date.toLocaleString(undefined, {
        year: 'numeric',
        month: 'numeric',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        // second: '2-digit', // Removed seconds
        // timeZoneName: 'short'
      });
    } catch {
      return 'Invalid date';
    }
  }
  function timeAgo(dateString: string | undefined) {
    if (!dateString) return '-';
    try {
      const date = new Date(dateString);
      const now = new Date();
      const diffMs = now.getTime() - date.getTime();
      const seconds = Math.floor(diffMs / 1000);
      let interval = Math.floor(seconds / 31536000);
      if (interval > 1) return interval + ' years ago';
      interval = Math.floor(seconds / 2592000);
      if (interval > 1) return interval + ' months ago';
      interval = Math.floor(seconds / 86400);
      if (interval > 1) return interval + ' days ago';
      interval = Math.floor(seconds / 3600);
      if (interval > 1) return interval + ' hours ago';
      interval = Math.floor(seconds / 60);
      if (interval > 1) return interval + ' minutes ago';
      return Math.floor(seconds) + ' seconds ago';
    } catch {
      return 'Unknown time';
    }
  }
</script>

<svelte:head>
  <title>Admin - Teams</title>
</svelte:head>

<div class="admin-card">
  <div class="admin-card-header">
    <div class="admin-card-title">Teams</div>
    <div class="batch-actions">
      <button class="button add-team-button" on:click={openAddForm}>Add Team</button>
    </div>
  </div>
  <div class="admin-stats-compact">
    <div class="stats-summary">
      <div class="stat-item">
        <span class="stat-label">Total:</span>
        <span class="stat-value-compact" style="color: #5b6eff;">{totalTeams}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">Accumulated Balances:</span>
        <span class="stat-value-compact" style="color: #5b6eff;">${totalBalance}</span>
      </div>
    </div>
  </div>
  <!-- Use the error from server load -->
  {#if error}
    <div class="error">{error}</div>
  {:else if teams.length === 0}
    <div class="empty-state">
      <p>Teams will appear here when you add them.</p>
    </div>
  {:else}
    <table class="client-table">
      <thead>
        <tr>
          <th style="padding: 4px 4px;">Actions</th>
          <th style="padding: 4px 4px;">ID</th>
          <th style="padding: 4px 4px;">Balance</th>
          <th style="padding: 4px 4px;">Next Charge</th>
          <th style="padding: 4px 4px;">Created At</th>
        </tr>
      </thead>
      <tbody>
        {#each teams as c}
          <tr>
            <td style="padding: 4px 4px;">
              <div style="display: flex; gap: 4px;">
                <form method="POST" action="?/delete" use:enhance={({ cancel }) => {
                  if (!confirm('Are you sure you want to delete this team?')) {
                    cancel();
                    return; // Prevent submission if cancelled
                  }

                  // If confirmed, return a function to handle the result
                  return async ({ result, update }) => {
                    // Let SvelteKit handle the default update or call update() if needed
                    // await update(); // No explicit update needed here for invalidatingAll

                    if (result.type === 'success') {
                      invalidateAll();
                    } else if (result.type === 'error') {
                      console.error('Delete failed:', result.error);
                      // Optionally, show an error message to the user
                    }
                  };
                }}>
                  <input type="hidden" name="id" value="{c.id}" />
                  <button class="button button-small button-danger" type="submit" style="padding: 4px 8px; font-size: 1em;">Delete</button>
                </form>
                <button class="button button-small" on:click={() => openEditForm(c)} style="padding: 4px 8px; font-size: 1em;">Edit</button>
              </div>
            </td>
            <td class="id-cell" style="padding: 4px 4px;"><span class="client-id">{c.id}</span></td>
            <td style="padding: 4px 4px;">${c.balance}</td>
            <td style="padding: 4px 4px;">{formatDate(c.next_charge_at)}</td>
            <td style="padding: 4px 4px;">{c.created_at ? timeAgo(c.created_at) : '-'}</td>
          </tr>
        {/each}
      </tbody>
    </table>
  {/if}
  <!-- Remove client-side timestamp and countdown -->
  <!--
  <div class="timestamp">
    Last updated: {formatDate(lastUpdated.toISOString())}
    <span class="refresh-countdown">(Next refresh in {nextRefreshIn}s)</span>
  </div>
  -->
</div>

