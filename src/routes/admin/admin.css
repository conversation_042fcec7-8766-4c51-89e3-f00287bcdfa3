html,
body {
  box-sizing: border-box;
  width: 100%;
}
*,
*::before,
*::after {
  box-sizing: inherit;
}

.admin-container {
  max-width: 1200px;
  margin: 0 auto;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0.75rem;
  /* border-bottom: 1px solid #333; */
}

.admin-title-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.admin-logo {
  height: 28px;
  width: auto;
  pointer-events: none;
}

.admin-title {
  font-size: 1.1rem;
  font-weight: 400;
  margin: 0;
}

.admin-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.admin-card {
  background-color: #1e1e1e;
  border-radius: 4px;
  padding: 1rem;
  margin-bottom: 1rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.admin-card-title {
  font-size: 1rem;
  font-weight: 400;
  margin-top: 0;
  margin-bottom: 1rem;
  display: flex;
  justify-content: left;
  align-items: center;
  text-align: center;
}

/* Tab Navigation Styles */
.admin-tabs {
  display: flex;
  gap: 0.25rem;
}

.admin-tab {
  padding: 0.5rem 1rem;
  border-radius: 6px 6px 0 0;
  color: #aaa;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  border-bottom: none;
}

.admin-tab:hover {
  color: white;
  background-color: rgba(255, 255, 255, 0.05);
}

.admin-tab.active {
  color: white;
  background-color: #1e1e1e;
  border-color: #333;
  border-bottom-color: #2a2a2a;
  position: relative;
  top: 1px;
}

.admin-tab-content {
  min-height: 200px;
}

.admin-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.admin-stats-compact {
  margin-bottom: 1rem;
}

.stats-summary {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 0.6rem 0.75rem;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  white-space: nowrap;
}

.stat-card {
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  margin: 0.5rem 0;
  color: #5b6eff;
}

.stat-value-compact {
  font-size: 1.2rem;
  font-weight: 700;
  color: #5b6eff;
}

.stat-label {
  font-size: 0.85rem;
  color: #aaa;
}

.client-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 0.75rem;
}

/* Remove fixed table layout to allow natural spacing */
.client-table {
  table-layout: auto;
}

/* Set minimal widths for columns */
.client-table th:nth-child(1) {
  width: 70px;
  max-width: 70px;
}

.client-table th:nth-child(2) {
  width: 45px;
  max-width: 45px;
}

.client-table th:nth-child(3) {
  width: 100px;
  max-width: 100px;
}

.client-table th:nth-child(4) {
  width: 70px;
  max-width: 70px;
}

.client-table th:nth-child(5) {
  width: 85px;
  max-width: 85px;
}

.client-table th:nth-child(6) {
  width: 65px;
  max-width: 65px;
}

.client-table th:nth-child(7) {
  width: 85px;
  max-width: 85px;
}

.client-table th,
.client-table td {
  padding: 0.2rem 0.1rem;
  text-align: left;
  border-bottom: 1px solid #333;
  font-size: 0.8rem;
}

.id-cell {
  text-align: center;
}

.status-cell {
  text-align: center;
}

.sync-cell {
  text-align: center;
}

.client-id {
  color: #ffeb3b; /* Yellow color for client ID */
  font-weight: 700;
}

.client-table th {
  font-weight: 600;
  color: #aaa;
  font-size: 0.75rem;
}

.client-table tr:hover {
  background-color: rgba(255, 255, 255, 0.05);
}

.status-enabled {
  color: #4caf50;
  font-weight: 500;
  font-size: 0.75rem;
}

.status-disabled {
  color: #f44336;
  font-weight: 500;
  font-size: 0.75rem;
}

.status-combined {
  display: flex;
  align-items: center;
  gap: 2px;
  white-space: nowrap;
  justify-content: center;
}

.status-separator {
  color: #666;
  font-size: 0.7rem;
  margin: 0 3px;
}

.sync-status {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.75rem;
  min-width: 60px;
}

.sync-status::before {
  content: "";
  display: inline-block;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  margin-right: 4px;
}

.sync-synced {
  color: #4caf50;
  background-color: rgba(76, 175, 80, 0.1);
}

.sync-synced::before {
  background-color: #4caf50;
}

.sync-pending {
  color: #ff9800;
  background-color: rgba(255, 152, 0, 0.1);
}

.sync-pending::before {
  background-color: #ff9800;
}

/* Conflict status removed as we now automatically trust the node */

.sync-unknown {
  color: #9e9e9e;
  background-color: rgba(158, 158, 158, 0.1);
}

.sync-unknown::before {
  background-color: #9e9e9e;
}

/* Button styles moved to the APK Repacker section below */

.button-secondary {
  background: #333;
  color: white;
}

.button-small {
  padding: 0.3rem 0.5rem;
  font-size: 0.8rem;
  min-width: 55px;
}

.button-danger {
  background: linear-gradient(
    to right,
    #f44336,
    #ff9800
  ) !important;
}

.button-success {
  background: linear-gradient(
    to right,
    #4caf50,
    #8bc34a
  ); /* Green for "Turn On" */
}

/* Client-specific button styles */
.client-toggle-btn {
  padding: 0.1rem 0.25rem !important;
  font-size: 0.65rem !important;
  min-width: 40px !important;
  max-width: 70px !important;
  border-radius: 3px !important;
  font-weight: 500 !important;
  box-shadow: none !important;
  transform: none !important;
  transition: opacity 0.2s ease !important;
  white-space: nowrap !important;
  width: auto !important;
  height: auto !important;
  line-height: normal !important;
}

.client-btn-off {
  background: #f44336 !important; /* Red for "Turn Off" */
}

.client-btn-on {
  background: #4caf50 !important; /* Green for "Turn On" */
}

.client-toggle-btn:hover {
  opacity: 0.85 !important;
  transform: none !important;
}

.client-trust-btn {
  padding: 0.1rem 0.25rem !important;
  font-size: 0.65rem !important;
  min-width: 40px !important;
  background: #333 !important;
  border-radius: 3px !important;
  font-weight: 500 !important;
  box-shadow: none !important;
  white-space: nowrap !important;
  width: auto !important;
  height: auto !important;
  line-height: normal !important;
}

.login-container {
  max-width: 400px;
  margin: 100px auto;
  padding: 1rem;
  background-color: #1e1e1e;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  margin-bottom: 0.3rem;
  font-size: 0.9rem;
  color: #fff;
  font-weight: 500;
}

.form-input {
  width: 100%;
  padding: 0.8rem;
  border-radius: 6px;
  border: 1px solid #333;
  background-color: #2a2a2a;
  color: #fff;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-input:focus {
  outline: none;
  border-color: #5b6eff;
}

.form-error {
  color: #f44336;
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

.form-submit {
  width: 100%;
  padding: 0.8rem;
  margin-top: 0rem;
}

.loading {
  opacity: 0.7;
  pointer-events: none;
}

.timestamp {
  font-size: 0.75rem;
  color: #aaa;
  margin-top: 0.75rem;
  text-align: right;
}

.refresh-countdown {
  color: #5b6eff;
  margin-left: 0.5rem;
  font-weight: 500;
}

.empty-state {
  text-align: center;
  padding: 1.5rem 1rem;
  color: #aaa;
  font-size: 0.9rem;
}

/* Toast message styling */
.toast-container {
  position: fixed;
  bottom: calc(10px + env(safe-area-inset-bottom));
  left: 0;
  right: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  pointer-events: none;
  padding: 0 10px; /* Add horizontal padding to prevent edge overflow on small screens */
  box-sizing: border-box;
}

.toast-message {
  background-color: #1b5e20;
  color: white;
  padding: 12px 16px; /* Slightly reduced padding for better fit on narrow screens */
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 600px;
  text-align: center;
  pointer-events: auto;
  opacity: 1 !important;
  transform: translateY(0) !important;
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
  margin-bottom: 10px;
  font-weight: 500;
  word-wrap: break-word;
  box-sizing: border-box;
}

/* Make sure toast is fully visible */
.toast-message.visible {
  opacity: 1;
  transform: translateY(0);
}

.toast-error {
  background-color: #b71c1c;
}

.empty-state p {
  margin: 0.5rem 0;
}

.node-status {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.node-active {
  background-color: #4caf50;
}

.node-inactive {
  background-color: #f44336;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #333;
  transition: 0.4s;
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #4caf50;
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* APK-REPACKER STYLES */
.admin-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.no-templates-message {
  color: #d32f2f;
  padding: 10px;
  border: 1px solid #d32f2f;
  border-radius: 4px;
  background-color: #ffebee;
  margin-bottom: 10px;
}

.apk-repacker-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.batch-actions {
  display: flex;
  gap: 10px;
}

.repack-all-button {
  background-color: #4caf50;
  color: white;
}

.repack-all-button:hover:not(:disabled) {
  background-color: #388e3c;
}

.download-all-button {
  background-color: #2196f3;
  color: white;
  font-weight: bold;
  padding: 10px 15px;
  animation: pulse-blue 1.5s infinite;
}

.download-all-button:hover {
  background-color: #1976d2;
  animation: none;
}

@keyframes pulse-blue {
  0% {
    transform: scale(0.98);
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 5px rgba(33, 150, 243, 0);
  }

  100% {
    transform: scale(0.98);
    box-shadow: 0 0 0 0 rgba(33, 150, 243, 0);
  }
}

.download-ready-container {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
}

.download-ready-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #4caf50;
  animation: fadeIn 0.5s ease-in-out;
  background-color: rgba(76, 175, 80, 0.1);
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid rgba(76, 175, 80, 0.3);
}

.download-ready-dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: #4caf50;
  animation: pulse 1.5s infinite;
}

.download-ready-text {
  font-weight: bold;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }

  70% {
    transform: scale(1);
    box-shadow: 0 0 0 5px rgba(76, 175, 80, 0);
  }

  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@media (max-width: 768px) {
  .admin-header {
    /* Keep row layout instead of column */
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
    padding-bottom: 0.5rem;
    margin-bottom: 0.5rem;
  }

  /* Mobile tab styles */
  .admin-tabs {
    overflow-x: auto;
    white-space: nowrap;
    gap: 0.1rem;
  }

  .admin-tab {
    padding: 0.4rem 0.75rem;
    font-size: 0.8rem;
  }

  .admin-title-container {
    flex-shrink: 1;
  }

  .admin-title {
    font-size: 0.95rem; /* Slightly smaller title on mobile */
    pointer-events: none;
  }

  .admin-logo {
    height: 24px;
    pointer-events: none;
  }

  .admin-actions {
    flex-shrink: 0;
    gap: 0.4rem;
  }

  .admin-card {
    padding: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .admin-stats {
    grid-template-columns: 1fr;
  }

  /* Keep stats-summary horizontal on mobile as well */
  .stats-summary {
    justify-content: space-around;
    padding: 0.5rem 0.4rem;
  }

  .stat-item {
    gap: 0.3rem;
  }

  .stat-value-compact {
    font-size: 1.1rem;
  }

  .client-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .client-table th,
  .client-table td {
    padding: 0.4rem 0.5rem;
    font-size: 0.75rem;
  }

  .client-table th {
    font-size: 0.7rem;
  }

  /* Adjust column widths for mobile */
  .client-table th:nth-child(1),
  .client-table td:nth-child(1) {
    width: 70px;
    min-width: 70px;
  }

  .client-table th:nth-child(2),
  .client-table td:nth-child(2) {
    width: 60px;
    min-width: 60px;
  }

  .client-table th:nth-child(3),
  .client-table td:nth-child(3) {
    width: 80px;
    min-width: 80px;
  }

  .client-table th:nth-child(4),
  .client-table td:nth-child(4) {
    width: 80px;
    min-width: 80px;
  }

  .client-table th:nth-child(6),
  .client-table td:nth-child(6) {
    width: 60px;
    min-width: 60px;
  }

  /* Make buttons more compact on mobile */
  .button {
    /* padding: 0.3rem 0.5rem; */
    font-size: 0.7rem;
  }

  .button-small {
    /* padding: 0.2rem 0.4rem; */
    font-size: 0.1rem;
    /* min-width: 50px; */
  }

  .empty-state {
    padding: 0.5rem 0rem;
  }

  .timestamp {
    font-size: 0.7rem;
    margin-top: 0.5rem;
  }
}

/* APK Repacker Styles */
.asset-filename-container {
  margin-top: 10px;
}

.asset-filename-input {
  min-height: unset !important;
  height: auto !important;
  padding: 8px !important;
  margin-bottom: 0 !important;
  resize: none !important;
}

.asset-filename-help {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.apk-repacker-container {
  padding: 1rem 0;
}

.horizontal-layout {
  display: flex;
  flex-wrap: wrap;
  align-items: stretch;
  gap: 1rem;
}

.horizontal-layout .form-group {
  flex: 1;
  min-width: 200px;
  display: flex;
  flex-direction: column;
}

.file-upload-group {
  display: flex;
  flex-direction: column;
}

.admin-card-description {
  color: #aaa;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  padding: 0 0.5rem;
}

.file-drop-area {
  border: 1px solid #444;
  border-radius: 4px;
  padding: 1rem;
  text-align: center;
  background-color: #2a2a2a;
  transition: all 0.2s ease;
  cursor: pointer;
  margin-bottom: 0;
  height: 100%;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.file-drop-area.active {
  border-color: #5b6eff;
  background-color: #2d2d3a;
}

.file-drop-area.has-file {
  border-color: #5b6eff;
  background-color: #2a2a2a;
}

.drop-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.3rem;
}

.browse-button {
  display: inline-block;
  padding: 0.4rem 0.8rem;
  background-color: #5b6eff; /* Blue for secondary actions */
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: none;
  font-size: 0.9rem;
  font-weight: 500;
  font-family: inherit;
}

.browse-button:hover {
  background-color: #4a59e0;
}

.file-requirements {
  font-size: 0.75rem;
  color: #aaa;
  margin-top: 0.3rem;
}

.selected-file {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: #2d2d3a;
  border-radius: 4px;
  padding: 0.5rem 0.75rem;
  width: 100%;
}

.file-name {
  font-weight: 500;
  max-width: 250px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: #fff;
}

.file-size {
  color: #aaa;
  font-size: 0.85rem;
}

.clear-file {
  background: none;
  border: none;
  color: #ff5252;
  font-size: 1.2rem;
  cursor: pointer;
  padding: 0 0.5rem;
  margin-left: auto;
}

.config-textarea {
  width: 100%;
  height: 100%;
  min-height: 180px;
  background-color: #2a2a2a;
  color: #fff;
  border: 1px solid #444;
  border-radius: 4px;
  padding: 0.6rem;
  font-family: monospace;
  font-size: 0.9rem;
  resize: vertical;
  line-height: 1.4;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  gap: 0.5rem;
}

.button-fallback {
  background-color: #666;
  font-size: 0.8rem;
}

.button-fallback:hover {
  background-color: #777;
}

/* Enhanced button styles */
.button {
  background: linear-gradient(to right, #5b6eff, #ec6fcd);
  color: white;
  padding: 0.5rem 0.9rem;
  border: none;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.button:active {
  transform: translateY(0);
}

/* Primary action button */
button[type="submit"].button {
  background: linear-gradient(
    to right,
    #5b6eff,
    #ec6fcd
  ); /* Purple gradient for all buttons */
}

.button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.download-button {
  background: linear-gradient(
    to right,
    #5b6eff,
    #ec6fcd
  ); /* Purple gradient for all buttons */
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.download-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.download-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  width: 100%;
}

.reset-button {
  background: linear-gradient(to right, #2196f3, #03a9f4); /* Blue gradient */
  flex: 1;
  color: #fff;
}

.reset-button:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.message-container {
  margin-top: 1rem;
}

.message {
  padding: 0.75rem 1rem;
  border-radius: 4px;
  font-size: 0.9rem;
}

.message.success {
  background-color: #2d3a2d;
  color: #4caf50;
  border: 1px solid #4caf50;
}

.message.error {
  background-color: #3a2d2d;
  color: #ff5252;
  border: 1px solid #ff5252;
}

.error-details {
  margin-top: 0.5rem;
  font-size: 0.8rem;
  max-height: 200px;
  overflow-y: auto;
  background-color: #332929;
  padding: 0.5rem;
  border-radius: 4px;
}

/* Mobile styles for APK Repacker */
@media (max-width: 768px) {
  .horizontal-layout {
    flex-direction: column;
  }

  .file-drop-area {
    padding: 1rem;
    min-height: 100px;
  }

  .config-textarea {
    min-height: 160px;
  }

  .selected-file {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
  }

  .file-name {
    max-width: 200px;
  }

  .form-actions {
    justify-content: stretch;
    flex-wrap: wrap;
    margin-top: 0rem;
  }

  .download-actions {
    flex-direction: column;
    width: 100%;
    gap: 0.5rem;
  }

  .button,
  .download-button,
  .reset-button {
    width: 100%;
    padding: 0.6rem 1rem;
    margin-top: 0.5rem;
  }
}

@media (max-width: 480px) {
  .toast-container {
    bottom: 10px;
    width: 100%;
    padding: 0 10px;
    box-sizing: border-box;
  }

  .toast-message {
    padding: 10px 16px;
    font-size: 0.85rem;
    max-width: 100%;
    margin-bottom: 5px;
    min-width: 0;
    width: 100%;
    box-sizing: border-box;
  }
}

.text-link {
  color: #5b6eff;
  text-decoration: none;
  transition: color 0.2s ease;
}

.text-link:hover {
  color: #8a9bff;
  text-decoration: underline;
}

.add-team-button {
  /* Inherits general styles from .button */
  background-color: #4caf50; /* Green background, matches .repall-button */
  color: white;
  padding: 0.5rem 0.9rem; /* Added padding from .button */
  border-radius: 6px; /* Added border-radius from .button */
  font-size: 0.85rem; /* Added font-size from .button */
  font-weight: 600; /* Added font-weight from .button */
  cursor: pointer; /* Added cursor from .button */
  transition: all 0.2s ease; /* Added transition from .button */
  white-space: nowrap; /* Added white-space from .button */
}

.add-team-button:hover:not(:disabled) {
  background-color: #388e3c; /* Darker green on hover, matches .repack-all-button */
  opacity: 0.9; /* Added opacity from .button:hover */
  transform: translateY(-1px); /* Added transform from .button:hover */
}

/* Remove specific active state, use general .button:active */
/* .add-team-button:active {
  transform: translateY(0);
} */

.team-info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Flexible columns */
    gap: 1rem;
    margin-bottom: 1rem;
}

.team-info-item {
    background-color: #2a2a2a; /* Match card background or a slightly different shade */
    border-radius: 6px;
    padding: 0.75rem 1rem;
    word-break: break-word; /* Prevent long IDs from overflowing */
}

.team-info-item strong {
    display: inline-block; /* Ensure strong doesn't take full width */
    margin-right: 0.5rem;
    color: #aaa; /* Lighter color for labels */
    font-weight: 500;
}

.date-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.date-group div {
    /* Styles for individual date items within the group */
}

/* Additional styles if needed for specific items */
.team-info-item strong[style="font-size: 0.8em;"] {
    color: #bbb; /* Slightly different color or style for smaller label */
}

.team-info-item strong[style="font-size: 0.9em;"] {
     color: #bbb; /* Slightly different color or style for smaller label */
}

/* Team page specific styles */
.section {
    margin-top: 2rem;
}

.table-container {
    margin-top: 1rem;
    overflow-x: auto;
    border-radius: 0.5rem;
    border: 1px solid #2d3748;
}

.role-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: capitalize;
    background-color: #e2e8f0;
    color: #4a5568;
}

.role-admin {
    background-color: #fef2f2;
    color: #dc2626;
}

.role-user {
    background-color: #eff6ff;
    color: #2563eb;
}

.text-muted {
    color: #a0aec0;
}

/* Danger zone styles - more compact and less red */
.danger-section {
    border: 1px solid #f59e0b;
    border-radius: 0.5rem;
    background-color: #451a03;
    padding: 1rem;
    margin-top: 2rem;
}

.danger-section h2 {
    color: #fbbf24;
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.danger-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1.5rem;
}

.danger-info {
    flex: 1;
}

.danger-info h3 {
    color: #fbbf24;
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.danger-info p {
    color: #fcd34d;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
}

.deletion-summary {
    background-color: #78350f;
    padding: 0.75rem;
    border-radius: 0.25rem;
    border: 1px solid #f59e0b;
}

.deletion-summary h4 {
    color: #fbbf24;
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
}

.deletion-summary ul {
    margin: 0;
    padding-left: 1.5rem;
    color: #fcd34d;
    font-size: 0.85rem;
}

.deletion-summary li {
    margin-bottom: 0.2rem;
}

.button-danger {
    background-color: #f59e0b;
    color: white;
    border: 1px solid #f59e0b;
}

.button-danger:hover {
    background-color: #d97706;
    border-color: #d97706;
}

.delete-confirmation {
    text-align: center;
}

.delete-confirmation p {
    color: #fbbf24;
    margin-bottom: 0.75rem;
    font-weight: 500;
    font-size: 0.9rem;
}

.confirmation-buttons {
    display: flex;
    gap: 0.75rem;
    justify-content: center;
}

@media (max-width: 768px) {
    .danger-content {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .confirmation-buttons {
        flex-direction: column;
        gap: 0.5rem;
    }

    .danger-section {
        padding: 0.75rem;
        margin-top: 1.5rem;
    }
}
