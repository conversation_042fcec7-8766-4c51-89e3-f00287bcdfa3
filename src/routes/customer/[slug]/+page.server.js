import { supabase } from "$lib/server/supabase";

/** @type {import('./$types').PageServerLoad} */
export async function load({ parent }) {
  const { team, deviceIp } = await parent();

  // Fetch transactions for this team
  let transactions = [];
  try {
    const { data: transactionData, error: transactionError } = await supabase
      .from('transactions')
      .select('*')
      .eq('team_id', team.id)
      .order('created_at', { ascending: false })
      .limit(20); // Limit to last 20 transactions

    if (transactionError) {
      console.error('Error fetching transactions:', transactionError);
    } else {
      transactions = transactionData || [];
    }
  } catch (err) {
    console.error('Error in transaction fetch:', err);
  }

  return { team, deviceIp, transactions };
}
