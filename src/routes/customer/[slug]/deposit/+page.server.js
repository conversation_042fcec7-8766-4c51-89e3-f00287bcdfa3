// @ts-nocheck
import { getTeamWallets } from '$lib/server/walletService.js';

/** @type {import('./$types').PageServerLoad} */
export async function load({ parent }) {
  const { team, deviceIp } = await parent();

  // Fetch wallets for this team
  let wallets = [];
  try {
    wallets = await getTeamWallets(team.id);
  } catch (walletError) {
    console.error('Error fetching wallets for deposit page:', walletError);
    // Don't throw error, just return empty wallets array
  }

  return {
    team,
    deviceIp,
    wallets: wallets || []
  };
}

// Note: The old form-based deposit action has been removed since we now display
// crypto wallet addresses directly. Deposits are processed via the IPN webhook
// at /api/notifications/ipn when payments are received at the wallet addresses.
