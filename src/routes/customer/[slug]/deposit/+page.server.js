// @ts-nocheck
import { fail } from '@sveltejs/kit';
import { supabase } from '$lib/server/supabase';
import { v4 as uuidv4 } from 'uuid';

export const actions = {
  default: async (event) => {
    const { request, params } = event;

    try {
      // Load device data from the database
      const { data: deviceData, error: deviceError } = await supabase
        .from('devices')
        .select('team_id')
        .eq('ip', params.slug)
        .single();
        
      if (deviceError || !deviceData) {
        const errorMsg = 'Device not found';
        console.error('[Deposit] Error:', errorMsg);
        return fail(400, { 
          error: 'Device not found',
          _debug: process.env.NODE_ENV === 'development' ? errorMsg : undefined
        });
      }
      
      // Load team data from the database
      const { data: teamData, error: teamError } = await supabase
        .from('teams')
        .select('*')
        .eq('id', deviceData.team_id)
        .single();
        
      if (teamError || !teamData) {
        const errorMsg = 'Team not found';
        console.error('[Deposit] Error:', errorMsg);
        return fail(400, { 
          error: 'Team not found',
          _debug: process.env.NODE_ENV === 'development' ? errorMsg : undefined
        });
      }

      if (!teamData) {
        const errorMsg = 'No team data found';
        console.error('[Deposit] Error:', errorMsg);
        return fail(400, { 
          error: 'Team information not found. Please try again.',
          _debug: process.env.NODE_ENV === 'development' ? errorMsg : undefined
        });
      }
      const data = await request.formData();
      const amount = parseFloat(data.get('amount')?.toString() || '0');
      const ipAddress = params.slug; // This is the IP address from the URL

      // Validate input
      if (!amount || isNaN(amount) || amount <= 0) {
        const errorMsg = `Invalid amount: ${amount}`;
        console.error('[Deposit] Validation error:', errorMsg);
        return fail(400, { 
          error: 'Please enter a valid amount',
          _debug: process.env.NODE_ENV === 'development' ? errorMsg : undefined
        });
      }

      // Generate a numeric ID using timestamp plus a random number
      const now = new Date();
      const depositId = BigInt(now.getTime()) * 1000n + BigInt(Math.floor(Math.random() * 1000));
      const nowISO = now.toISOString();
      const rate = 1;
      const currency = 'USD';
      const expiresAt = new Date(Date.now() + 60 * 60 * 1000).toISOString();
      const wallet = `ph_${depositId.toString().slice(-16)}`;

      // Create deposit record
      const { data: depositData, error: depositError } = await supabase
        .from('deposits')
        .insert({
          internal_id: depositId.toString(),
          created_at: nowISO,
          team_internal_id: teamData.internal_id,
          team_id: teamData.id,
          amount: amount,
          wallet: wallet,
          currency: currency,
          expires_at: expiresAt,
          rate: rate
        })
        .select()
        .single();

      if (depositError) {
        const errorMsg = `Deposit creation failed: ${depositError.message}`;
        console.error('[Deposit] Database error:', {
          message: depositError.message,
          details: depositError.details,
          hint: depositError.hint,
          code: depositError.code
        });
        throw new Error(errorMsg);
      }

      if (!depositData) {
        throw new Error('Failed to retrieve created deposit record');
      }

      // Redirect to payment URL (in a real app, this would be your payment processor's URL)
      const paymentUrl = `https://pay.phantom.app/pay/${depositId}`;
      
      // Return the payment URL and details
      return {
        success: true,
        paymentUrl,
        wallet,
        amount,
        currency,
        expiresAt,
        depositId
      };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
      const errorStack = error instanceof Error ? error.stack : undefined;
      const errorName = error instanceof Error ? error.name : 'UnknownError';
      
      const logData = {
        message: errorMessage,
        stack: errorStack,
        timestamp: new Date().toISOString(),
        url: event?.url?.toString(),
        method: event?.request?.method,
        params: event?.params
      };
      
      console.error(`[Deposit] ${errorName}:`, logData);
      
      return fail(500, { 
        error: 'An error occurred while processing your deposit. Please try again.',
        _debug: process.env.NODE_ENV === 'development' ? {
          message: errorMessage,
          stack: errorStack,
          name: errorName
        } : undefined
      });
    }
  }
};
