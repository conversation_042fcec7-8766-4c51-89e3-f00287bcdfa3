<script>
  import { onMount } from "svelte";
  import { goto } from "$app/navigation";
  import { enhance } from "$app/forms";
  import { page } from '$app/stores';
  import "../customer.css";
  import toastStore from '$lib/stores/toastStore';

  /** @type {import('./$types').PageData} */
  export let data;
  const { team, deviceIp } = data;

  let amount = "";
  let bgLoaded = false;
  /** @type {HTMLInputElement | null} */
  let amountInput = null;

  onMount(() => {
    // Focus the amount input when the component mounts
    if (amountInput) {
      amountInput.focus({ preventScroll: true });
    }
    
    /** @param {Event} e */
    const handleTouchMove = (e) => {
      // Cast target to HTMLElement to access tagName
      const target = /** @type {HTMLElement} */ (e.target);
      if (target.tagName !== "INPUT" && target.tagName !== "BUTTON") {
        e.preventDefault();
      }
    };
    
    // Add touchmove event listener with proper typing
    document.body.addEventListener("touchmove", handleTouchMove, false);
    
    return () => {
      document.body.removeEventListener("touchmove", handleTouchMove, false);
    };
  });

  // Handle form submission result
  $: if ($page.form?.success) {
    // Redirect to payment URL
    window.location.href = $page.form.paymentUrl;
  } else if ($page.form?.error) {
    toastStore.add($page.form.error, 'error');
  }
</script>

<svelte:head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="theme-color" content="#121212" />
  <title>Deposit - Phantom</title>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous" />
  <link rel="preload" as="image" href="/bg.webp" />
</svelte:head>

<div class="bg-image-container" class:loaded={bgLoaded}>
  <img src="/bg.webp" alt="Background" class="bg-image" loading="lazy" on:load={() => (bgLoaded = true)} crossorigin="anonymous" />
</div>

<div class="app-container">
  <div class="header-card">
    <div class="header">
      <div class="title-container">
        <img src="/favicon.webp" class="logo-image" alt="Phantom" width="36" height="36" loading="eager" />
        <h1>Add Funds</h1>
      </div>
    </div>
  </div>

  <div class="deposit-card">
    <form method="POST" use:enhance>
      <div class="form-group">
        <label for="amount">Amount (USD)</label>
        <input
          type="number"
          id="amount"
          name="amount"
          bind:value={amount}
          placeholder="0.00"
          min="0.01"
          step="0.01"
          inputmode="decimal"
          bind:this={amountInput}
          disabled={$page.form?.success}
          aria-label="Deposit amount in USD"
          required
        />
      </div>

      <div class="quick-amounts">
        {#each [50, 100, 150, 300] as quickAmount}
          <button
            type="button"
            class="quick-amount"
            class:selected={amount === quickAmount.toString()}
            on:click|preventDefault={() => {
              amount = quickAmount.toString();
              // Focus the input after setting the value
              if (amountInput) amountInput.focus();
            }}
            disabled={$page.form?.success}
          >
            ${quickAmount}
          </button>
        {/each}
      </div>

      <button
        type="submit"
        class="deposit-button"
        class:loading={$page.form?.success}
        disabled={!amount || $page.form?.success}
        aria-busy={$page.form?.success}
      >
        {#if $page.form?.success}
          Redirecting...
        {:else}
          Deposit ${amount || '0.00'}
        {/if}
      </button>
    </form>
  </div>

  <button class="back-button" on:click|preventDefault={() => goto(`/customer/${deviceIp}`)}>
    ← Back
  </button>
</div>