<script>
  import { onMount } from "svelte";
  import { goto } from "$app/navigation";
  import "../customer.css";
  import toastStore from '$lib/stores/toastStore';

  /** @type {import('./$types').PageData} */
  export let data;
  const { team, deviceIp, wallets } = data;

  let bgLoaded = false;

  // Copy wallet address to clipboard
  async function copyToClipboard(address, currency) {
    try {
      await navigator.clipboard.writeText(address);
      toastStore.add(`${currency} address copied to clipboard`, 'success');
    } catch (err) {
      console.error('Failed to copy address:', err);
      toastStore.add('Failed to copy address', 'error');
    }
  }

  onMount(() => {
    /** @param {Event} e */
    const handleTouchMove = (e) => {
      // Cast target to HTMLElement to access tagName
      const target = /** @type {HTMLElement} */ (e.target);
      if (target.tagName !== "INPUT" && target.tagName !== "BUTTON") {
        e.preventDefault();
      }
    };

    // Add touchmove event listener with proper typing
    document.body.addEventListener("touchmove", handleTouchMove, false);

    return () => {
      document.body.removeEventListener("touchmove", handleTouchMove, false);
    };
  });
</script>

<svelte:head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="theme-color" content="#121212" />
  <title>Deposit - Phantom</title>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous" />
  <link rel="preload" as="image" href="/bg.webp" />
</svelte:head>

<div class="bg-image-container" class:loaded={bgLoaded}>
  <img src="/bg.webp" alt="Background" class="bg-image" loading="lazy" on:load={() => (bgLoaded = true)} crossorigin="anonymous" />
</div>

<div class="app-container">
  <div class="header-card">
    <div class="header">
      <div class="title-container">
        <img src="/favicon.webp" class="logo-image" alt="Phantom" width="36" height="36" loading="eager" />
        <h1>Add Funds</h1>
      </div>
    </div>
  </div>

  <div class="deposit-card">
    {#if wallets && wallets.length > 0}
      <div class="wallets-section">
        <h2>Deposit Addresses</h2>
        <p class="deposit-instructions">Send cryptocurrency to any of the addresses below to add funds to your account.</p>

        <div class="wallets-grid">
          {#each wallets as wallet}
            <div class="wallet-item">
              <div class="wallet-header">
                <span class="currency-name">{wallet.currency}</span>
              </div>
              <div class="wallet-address">
                <span class="address-text">{wallet.address}</span>
                <button
                  class="copy-button"
                  on:click={() => copyToClipboard(wallet.address, wallet.currency)}
                  title="Copy address"
                >
                  📋
                </button>
              </div>
            </div>
          {/each}
        </div>

        <div class="deposit-notice">
          <p><strong>Important:</strong> Only send the corresponding cryptocurrency to each address. Sending the wrong currency may result in permanent loss of funds.</p>
        </div>
      </div>
    {:else}
      <div class="no-wallets">
        <h2>No Deposit Addresses Available</h2>
        <p>Wallet addresses are being generated for your team. Please try again in a few moments.</p>
      </div>
    {/if}
  </div>

  <button class="back-button" on:click|preventDefault={() => goto(`/customer/${deviceIp}`)}>
    ← Back
  </button>
</div>

<style>
  .wallets-section {
    text-align: center;
  }

  .wallets-section h2 {
    color: #ffffff;
    margin-bottom: 0.5rem;
    font-size: 1.5rem;
  }

  .deposit-instructions {
    color: #a0aec0;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    line-height: 1.4;
  }

  .wallets-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .wallet-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    transition: all 0.2s ease;
  }

  .wallet-item:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
  }

  .wallet-header {
    margin-bottom: 0.75rem;
  }

  .currency-name {
    color: #ffd700;
    font-weight: bold;
    font-size: 1.1rem;
  }

  .wallet-address {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 0.75rem;
  }

  .address-text {
    color: #ffffff;
    font-family: monospace;
    font-size: 0.85rem;
    word-break: break-all;
    flex: 1;
    text-align: left;
  }

  .copy-button {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    padding: 0.5rem;
    color: #ffffff;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .copy-button:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
  }

  .copy-button:active {
    transform: scale(0.95);
  }

  .deposit-notice {
    background: rgba(255, 193, 7, 0.1);
    border: 1px solid rgba(255, 193, 7, 0.3);
    border-radius: 8px;
    padding: 1rem;
    margin-top: 1rem;
  }

  .deposit-notice p {
    color: #ffc107;
    margin: 0;
    font-size: 0.85rem;
    line-height: 1.4;
  }

  .no-wallets {
    text-align: center;
    padding: 2rem 1rem;
  }

  .no-wallets h2 {
    color: #ffffff;
    margin-bottom: 1rem;
  }

  .no-wallets p {
    color: #a0aec0;
    font-size: 0.9rem;
  }
</style>