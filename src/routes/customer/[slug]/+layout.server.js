import { redirect, error } from "@sveltejs/kit";
import { env } from "$env/dynamic/private";
import { supabase } from "$lib/server/supabase";

const WEBVIEW_AUTH_TOKEN = env.WEBVIEW_AUTH_TOKEN || "01QIm8aW";

export async function load({ params, request, getClientAddress }) {
  // Skip auth header verification in local development
  if (process.env.NODE_ENV !== 'development') {
    // Authenticate request via X-Phantom-Auth header
    const authHeader = request.headers.get("x-phantom-auth");
    if (!authHeader || authHeader !== WEBVIEW_AUTH_TOKEN) {
      console.log("Unauthorized access attempt detected!");
      throw error(401, "Unauthorized");
    }
  } else {
    console.log("Running in development mode - auth header verification skipped");
  }

  const external_ip = request.headers.get("x-forwarded-for") || getClientAddress();
  const ip = params.slug;
  console.log(`[${external_ip}] auth as: ${ip}`);

  try {
    // First, find the device by IP
    const { data: device, error: deviceError } = await supabase
      .from("devices")
      .select("team_id")
      .eq("ip", ip)
      .single();

    if (deviceError || !device) {
      console.error("Error finding device by IP:", deviceError || "No device found");
      throw error(404, "Device not found for this IP");
    }

    // Then fetch the team using the team_id from the device
    const { data: team, error: teamError } = await supabase
      .from("teams")
      .select("*")
      .eq("id", device.team_id)
      .single();

    if (teamError || !team) {
      console.error("Error fetching team:", teamError || "Team not found");
      throw error(404, "Team not found");
    }

    // Update last_auth_at for the device
    const { error: updateError } = await supabase
      .from("devices")
      .update({ last_auth_at: new Date().toISOString() })
      .eq("ip", ip);

    if (updateError) {
      console.error("Error updating device last_auth_at:", updateError);
    }

    return {
      team,
      device,
      deviceIp: ip
    };
  } catch (err) {
    console.error("Error in layout server load:", err);
    // Only redirect if it's a 404 error (device/team not found)
    if (err && typeof err === 'object' && 'status' in err && err.status === 404) {
      throw redirect(303, '/');
    }
    throw err;
  }
}
