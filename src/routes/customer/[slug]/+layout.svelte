<script>
  import { page } from '$app/stores';
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import Toast from '$lib/components/Toast.svelte';
  import './customer.css'; // Using relative path since it's in the same directory

  let bgLoaded = false;

  onMount(() => {
    // Add any layout-specific initialization here
  });

  // Add any layout-specific logic here
</script>

<svelte:head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
  <meta name="theme-color" content="#121212" />
  <title>Phantom</title>
  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin="anonymous" />
  <link rel="preload" as="image" href="/bg.webp" />
</svelte:head>

<div class="bg-image-container" class:loaded={bgLoaded}>
  <img 
    src="/bg.webp" 
    alt="Background" 
    class="bg-image" 
    loading="lazy" 
    on:load={() => (bgLoaded = true)} 
    crossorigin="anonymous" 
  />
</div>

<div class="app-container">
  <slot />
</div>

<!-- Toast notifications will appear above all other content -->
<Toast />

<!-- Global styles -->
<style>
  /* These styles ensure proper layout for the toast container */
  :global(html), :global(body) {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
  }

  :global(#svelte) {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }
</style>
