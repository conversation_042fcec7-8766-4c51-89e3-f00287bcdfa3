import { error } from '@sveltejs/kit';
import fs from 'fs';
import path from 'path';
import { env } from '$env/dynamic/private';

// Authentication for accessing install files
const INSTALL_AUTH_HEADER = 'X-Phantom-Auth';
const INSTALL_DIR_PASSWORD = env.INSTALL_DIR_PASSWORD || '3NZMA99jRDQA5Aa4xzZCip';

// MIME type mapping for common file extensions
const MIME_TYPES = {
  '.sh': 'text/plain',
  '.txt': 'text/plain',
  '.md': 'text/markdown',
  '.js': 'application/javascript',
  '.json': 'application/json',
  '.html': 'text/html',
  '.css': 'text/css',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
  '.pdf': 'application/pdf',
  '.zip': 'application/zip',
  '.gz': 'application/gzip',
  '.tar': 'application/x-tar',
  '.bin': 'application/octet-stream',
  '.exe': 'application/octet-stream',
  '.apk': 'application/vnd.android.package-archive',
  '.deb': 'application/vnd.debian.binary-package',
  '.rpm': 'application/x-rpm'
};

/**
 * Get MIME type for a file based on its extension
 * @param {string} filePath - Path to the file
 * @returns {string} MIME type
 */
function getMimeType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  // Use a safer way to access the MIME type
  return Object.prototype.hasOwnProperty.call(MIME_TYPES, ext)
    ? MIME_TYPES[ext]
    : 'application/octet-stream';
}

/**
 * Create a 401 Unauthorized response
 * @returns {Response} HTTP response
 */
function createUnauthorizedResponse() {
  return new Response(
    null,
    {
      status: 401,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

/**
 * Handle GET requests to download files from the install directory
 * @param {Object} params - The request parameters
 * @param {Object} params.params - URL parameters
 * @param {string} params.params.file - The file path
 * @param {Object} params.request - The request object
 * @returns {Promise<Response>} The HTTP response with the file
 */
export async function GET({ params, request }) {
  // Get the file path from the params
  const filePath = params.file;

  // Get the custom auth header
  const authHeader = request.headers.get(INSTALL_AUTH_HEADER);

  // Check if the auth header is present
  if (!authHeader) {
    return createUnauthorizedResponse('Authentication required for install files');
  }

  // Check if the password matches
  if (authHeader !== INSTALL_DIR_PASSWORD) {
    return createUnauthorizedResponse('Invalid authentication');
  }

  // Validate file path (prevent directory traversal)
  if (filePath.includes('..') || filePath.startsWith('/') || filePath.startsWith('\\')) {
    throw error(400, 'Invalid file path');
  }

  // Try both ./install_files and ../install_files
  const possiblePaths = [
    // Current directory
    path.join('./install_files', filePath),

    // One level up
    path.join('../install_files', filePath),

    // In server directory
    path.join('./server/install_files', filePath)
  ];

  // Find the first path that exists
  let fullPath = null;
  for (const testPath of possiblePaths) {
    if (fs.existsSync(testPath)) {
      fullPath = testPath;
      break;
    }
  }

  // Log the paths we tried for debugging
  console.log('Looking for file:', filePath);
  console.log('Current directory:', process.cwd());
  console.log('Tried paths:', possiblePaths);
  console.log('Found path:', fullPath);

  // Check if we found a valid path
  if (!fullPath) {
    throw error(404, 'File not found');
  }

  try {
    // Read the file
    const fileBuffer = fs.readFileSync(fullPath);

    // Get the MIME type
    const mimeType = getMimeType(fullPath);

    // Determine if this should be downloaded or displayed inline
    const disposition = mimeType.startsWith('text/') || mimeType.startsWith('image/')
      ? 'inline'
      : 'attachment';

    // Return the file as a response
    return new Response(fileBuffer, {
      headers: {
        'Content-Type': mimeType,
        'Content-Disposition': `${disposition}; filename="${path.basename(filePath)}"`,
        'Content-Length': fileBuffer.length.toString(),
        'Cache-Control': 'no-store, no-cache, must-revalidate, proxy-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    });
  } catch (err) {
    console.error('Error reading file:', err);
    throw error(500, 'Error reading file');
  }
}
