import { json } from '@sveltejs/kit';
import { supabase } from '$lib/server/supabase';

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
  try {
    const { teamId, count = 5 } = await request.json();

    if (!teamId) {
      return json({ error: 'teamId is required' }, { status: 400 });
    }

    // First, get the team data to ensure it exists and get internal_id
    const { data: teamData, error: teamError } = await supabase
      .from('teams')
      .select('internal_id, id, balance')
      .eq('id', teamId)
      .single();

    if (teamError || !teamData) {
      return json({ error: 'Team not found' }, { status: 404 });
    }

    // Generate fake transactions
    const transactions = [];
    let currentBalance = Number(teamData.balance);
    
    // Sample transaction types and descriptions
    const transactionTypes = [
      { type: 'deposit', descriptions: ['USDT deposit', 'Bitcoin deposit', 'Ethereum deposit', 'Initial deposit', 'Top up'], amountRange: [10, 100] },
      { type: 'charge', descriptions: ['VPN Service - Monthly', 'Premium Service', 'Data Usage Charge', 'Service Fee'], amountRange: [-15, -5] },
      { type: 'bonus', descriptions: ['Referral bonus', 'Loyalty reward', 'Promotional credit'], amountRange: [5, 25] }
    ];

    for (let i = 0; i < count; i++) {
      // Random transaction type
      const transactionType = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];
      const description = transactionType.descriptions[Math.floor(Math.random() * transactionType.descriptions.length)];
      
      // Random amount within range
      const [minAmount, maxAmount] = transactionType.amountRange;
      const amount = Number((Math.random() * (maxAmount - minAmount) + minAmount).toFixed(2));
      
      // Calculate balances
      const balanceBefore = currentBalance;
      const balanceAfter = Number((currentBalance + amount).toFixed(2));
      currentBalance = balanceAfter;

      // Generate a unique transaction ID
      const transactionId = BigInt(Date.now()) * 1000n + BigInt(Math.floor(Math.random() * 1000)) + BigInt(i);
      
      // Random date within the last 60 days
      const daysAgo = Math.floor(Math.random() * 60);
      const createdAt = new Date(Date.now() - daysAgo * 24 * 60 * 60 * 1000).toISOString();

      transactions.push({
        internal_id: transactionId.toString(),
        created_at: createdAt,
        team_internal_id: teamData.internal_id,
        team_id: teamData.id,
        amount: amount,
        description: description,
        balance_before: balanceBefore,
        balance_after: balanceAfter
      });
    }

    // Sort transactions by date (oldest first for proper balance calculation)
    transactions.sort((a, b) => new Date(a.created_at) - new Date(b.created_at));

    // Recalculate balances in chronological order
    let runningBalance = Number(teamData.balance) - transactions.reduce((sum, t) => sum + t.amount, 0);
    transactions.forEach(transaction => {
      transaction.balance_before = Number(runningBalance.toFixed(2));
      runningBalance += transaction.amount;
      transaction.balance_after = Number(runningBalance.toFixed(2));
    });

    // Insert transactions into database
    const { data: insertedTransactions, error: insertError } = await supabase
      .from('transactions')
      .insert(transactions)
      .select();

    if (insertError) {
      console.error('Error inserting fake transactions:', insertError);
      return json({ error: 'Failed to create transactions', details: insertError.message }, { status: 500 });
    }

    return json({
      success: true,
      message: `Created ${insertedTransactions.length} fake transactions for team ${teamId}`,
      transactions: insertedTransactions
    });

  } catch (error) {
    console.error('Error in fake transactions API:', error);
    return json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/** @type {import('./$types').RequestHandler} */
export async function GET() {
  return json({
    endpoint: 'Fake Transactions Generator',
    usage: 'POST with { "teamId": "your-team-id", "count": 5 }',
    description: 'Generates fake transactions for testing purposes'
  });
}
