import { json } from '@sveltejs/kit';
import { supabase } from '$lib/server/supabase';
import { v4 as uuidv4 } from 'uuid';

/** @type {import('./$types').RequestHandler} */
export async function POST({ request }) {
  try {
    const { teamId, ip, nickname } = await request.json();

    if (!teamId || !ip) {
      return json({ error: 'teamId and ip are required' }, { status: 400 });
    }

    // First, get the team data to ensure it exists and get internal_id
    const { data: teamData, error: teamError } = await supabase
      .from('teams')
      .select('internal_id, id')
      .eq('id', teamId)
      .single();

    if (teamError || !teamData) {
      return json({ error: 'Team not found' }, { status: 404 });
    }

    // Check if device with this IP already exists
    const { data: existingDevice } = await supabase
      .from('devices')
      .select('ip')
      .eq('ip', ip)
      .single();

    if (existingDevice) {
      return json({ error: 'Device with this IP already exists' }, { status: 409 });
    }

    // Create the device
    const deviceData = {
      internal_id: uuidv4(),
      created_at: new Date().toISOString(),
      team_internal_id: teamData.internal_id,
      team_id: teamData.id,
      ip: ip,
      nickname: nickname || null,
      role: 'user',
      last_auth_at: null,
      vpn_conf: null,
      msg_conf: null,
      phone_conf: null,
      fcm_token: null
    };

    const { data: device, error: deviceError } = await supabase
      .from('devices')
      .insert([deviceData])
      .select()
      .single();

    if (deviceError) {
      console.error('Error creating device:', deviceError);
      return json({ error: 'Failed to create device', details: deviceError.message }, { status: 500 });
    }

    return json({
      success: true,
      message: `Created device with IP ${ip} for team ${teamId}`,
      device: device
    });

  } catch (error) {
    console.error('Error in test device API:', error);
    return json({
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

/** @type {import('./$types').RequestHandler} */
export async function GET() {
  return json({
    endpoint: 'Test Device Creator',
    usage: 'POST with { "teamId": "your-team-id", "ip": "*************", "nickname": "optional-nickname" }',
    description: 'Creates a test device for testing the customer interface'
  });
}
