// src/routes/api/[...slugs]/lib/api.js
import { Elysia, t } from "elysia";
import { ClientDatabase } from './database';
import { getClientIP, authenticate } from './helpers';

export class ApiController {
  /**
   * @param {ClientDatabase} db - The client database
   */
  constructor(db) {
    this.db = db;
  }

  /**
   * Handle VPN update request with two-way communication
   * @param {Object} body - The request body
   * @param {import('./helpers').ClientRequest[]} body.clients - List of clients
   * @param {Object} headers - The request headers
   * @param {string} [headers.authorization] - The authorization header
   * @param {Request} request - The HTTP request
   * @returns {Object} The response
   */
  handleVpnUpdate(body, headers, request) {
    try {
      authenticate({ headers });
      // Use the node's IP as its ID
      const nodeIp = getClientIP(request);

      // Update database with client data
      this.db.updateFromNode(nodeIp, body.clients);

      // Get any pending updates for clients on this node
      const clientUpdates = this.db.getPendingClientUpdates(nodeIp).map(update => ({
        ...update,
        timestamp: update.timestamp.toISOString() // Convert Date to string for JSON
      }));

      return {
        success: true,
        message: `Updated ${body.clients.length} clients from node ${nodeIp}`,
        nodeIp,
        timestamp: new Date().toISOString(),
        // Send any pending configuration changes back to the node
        client_updates: clientUpdates
      };
    } catch (error) {
      throw error;
    }
  }

  /**
   * Set client status (enabled/disabled)
   * @param {string} clientId - The client ID
   * @param {boolean} enabled - Whether the client should be enabled
   * @param {Object} headers - The request headers
   * @param {string} [headers.authorization] - The authorization header
   * @param {string} [userId] - The user making the change
   * @returns {Object} The response
   */
  setClientStatus(clientId, enabled, headers, userId) {
    try {
      authenticate({ headers });

      // Update client status in database
      const result = this.db.updateClientStatus(clientId, enabled, userId);

      if (result) {
        return {
          success: true,
          message: `Client ${clientId} ${enabled ? 'enabled' : 'disabled'} successfully`,
          timestamp: new Date().toISOString()
        };
      } else {
        throw new Error(`Client ${clientId} not found`);
      }
    } catch (error) {
      throw error;
    }
  }

  /**
   * Acknowledge client status update application
   * @param {string} clientId - The client ID
   * @param {string} updateId - The update ID
   * @param {boolean} [success=true] - Whether the update was successful
   * @returns {Object} The response
   */
  acknowledgeUpdate(clientId, updateId, success = true) {
    const client = this.db.getClient(clientId);

    if (!client) {
      throw new Error(`Client ${clientId} not found`);
    }

    const pendingUpdates = this.db.getPendingUpdatesForClient(clientId);
    const matchingUpdate = pendingUpdates.find(u => u.updateId === updateId);

    if (!matchingUpdate) {
      return {
        success: false,
        message: `No pending update found with ID ${updateId}`,
        timestamp: new Date().toISOString()
      };
    }

    // Handle the acknowledgment
    if (success) {
      // Mark as applied in history
      for (const entry of client.statusHistory) {
        if (entry.timestamp.getTime() === parseInt(updateId.split('-')[1])) {
          entry.appliedAt = new Date();
          break;
        }
      }

      // Complete the sync if this was a status update
      if (matchingUpdate.type === 'status') {
        client.actualState = client.desiredState;
        client.pendingSync = false;
      }

      // Remove this specific update
      this.db.removePendingUpdate(clientId, updateId);

      return {
        success: true,
        message: `Update ${updateId} for client ${clientId} acknowledged`,
        timestamp: new Date().toISOString()
      };
    } else {
      // Handle failed update
      return {
        success: false,
        message: `Node reported failure applying update`,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get all clients
   * @returns {Object} The response with all clients
   */
  getAllClients() {
    const clients = this.db.getAllClients().map(client => ({
      id: client.id,
      ips: client.ips,
      nodeId: client.nodeId,
      lastUpdate: client.lastUpdate,
      status: {
        desired: client.desiredState ? "enabled" : "disabled",
        actual: client.actualState ? "enabled" : "disabled",
        pending: client.pendingSync,
        lastChanged: client.lastStateChange,
        syncStatus: client.pendingSync ? "pending" :
                  (client.desiredState === client.actualState ? "synced" : "conflict")
      }
    }));

    return {
      clients,
      count: clients.length,
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * Generate a report of all clients
   * @returns {Object} The response
   */
  generateReport() {
    // Print all clients to console
    this.db.printAllClients();

    // Return a summary
    return {
      success: true,
      message: "Client report generated",
      timestamp: new Date().toISOString(),
      clientCount: this.db.getAllClients().length
    };
  }

  /**
   * Trust node's state (resolve conflict by making node state authoritative)
   * @param {string} clientId - The client ID
   * @param {Object} headers - The request headers
   * @param {string} [headers.authorization] - The authorization header
   * @param {string} [userId] - The user making the change
   * @returns {Object} The response
   */
  trustNodeState(clientId, headers, userId) {
    try {
      authenticate({ headers });

      // Update client to trust node's state
      const result = this.db.trustNodeState(clientId, userId);

      if (result) {
        return {
          success: true,
          message: `Client ${clientId} updated to trust node's state`,
          timestamp: new Date().toISOString()
        };
      } else {
        // If no update was made, it could be because there's no conflict
        const client = this.db.getClient(clientId);
        if (!client) {
          throw new Error(`Client ${clientId} not found`);
        }

        return {
          success: false,
          message: `No conflict to resolve for client ${clientId}`,
          timestamp: new Date().toISOString()
        };
      }
    } catch (error) {
      throw error;
    }
  }
}

export class RouteConfig {
  /**
   * @param {ApiController} apiController - The API controller
   */
  constructor(apiController) {
    this.apiController = apiController;
  }

  /**
   * Configure routes for the API
   * @param {Elysia} app - The Elysia app
   * @returns {Elysia} The configured app
   */
  configureRoutes(app) {
    return app
      .get("/ip", ({request}) => {
        return getClientIP(request);
      })
      .post(
        "/vpn",
        ({ body, headers, set, request }) => {
          try {
            const result = this.apiController.handleVpnUpdate(body, headers, request);
            return result;
          } catch (error) {
            set.status = 401;
            return {
              success: false,
              message: error instanceof Error ? error.message : "Unknown error",
              timestamp: new Date().toISOString(),
            };
          }
        },
        {
          body: t.Object({
            clients: t.Array(
              t.Object({
                client_id: t.String(),
                allowed_ips: t.String(),
                enabled: t.Optional(t.Boolean())
              })
            ),
          }),
          detail: {
            summary: "Update clients from a node",
            tags: ["Clients"],
          },
        }
      )
      .post(
        "/client/:clientId/status",
        ({ params, body, headers, set }) => {
          try {
            return this.apiController.setClientStatus(
              params.clientId,
              body.enabled,
              headers
            );
          } catch (error) {
            set.status = error instanceof Error && error.message.includes("not found") ? 404 : 401;
            return {
              success: false,
              message: error instanceof Error ? error.message : "Unknown error",
              timestamp: new Date().toISOString(),
            };
          }
        },
        {
          params: t.Object({
            clientId: t.String(),
          }),
          body: t.Object({
            enabled: t.Boolean(),
          }),
          detail: {
            summary: "Set client enabled/disabled status",
            tags: ["Admin"],
          },
        }
      )
      .post(
        "/client/:clientId/acknowledge",
        ({ params, body, headers, set }) => {
          try {
            authenticate({ headers });
            return this.apiController.acknowledgeUpdate(
              params.clientId,
              body.updateId,
              body.success
            );
          } catch (error) {
            set.status = error instanceof Error && error.message.includes("not found") ? 404 : 401;
            return {
              success: false,
              message: error instanceof Error ? error.message : "Unknown error",
              timestamp: new Date().toISOString(),
            };
          }
        },
        {
          params: t.Object({
            clientId: t.String(),
          }),
          body: t.Object({
            updateId: t.String(),
            success: t.Boolean()
          }),
          detail: {
            summary: "Acknowledge client status update application",
            tags: ["Nodes"],
          },
        }
      )
      .get(
        "/clients",
        ({ headers, set }) => {
          try {
            authenticate({ headers });
            return this.apiController.getAllClients();
          } catch (error) {
            set.status = 401;
            return {
              success: false,
              message: error instanceof Error ? error.message : "Unknown error",
            };
          }
        },
        {
          detail: {
            summary: "Get all clients",
            tags: ["Admin"],
          },
        }
      )
      .get(
        "/report",
        ({ headers, set }) => {
          try {
            authenticate({ headers });
            return this.apiController.generateReport();
          } catch (error) {
            set.status = 401;
            return {
              success: false,
              message: error instanceof Error ? error.message : "Unknown error",
            };
          }
        },
        {
          detail: {
            summary: "Trigger client report",
            tags: ["Admin"],
          },
        }
      )
      .post(
        "/client/:clientId/trust-node",
        ({ params, headers, set }) => {
          try {
            return this.apiController.trustNodeState(
              params.clientId,
              headers
            );
          } catch (error) {
            set.status = error instanceof Error && error.message.includes("not found") ? 404 : 401;
            return {
              success: false,
              message: error instanceof Error ? error.message : "Unknown error",
              timestamp: new Date().toISOString(),
            };
          }
        },
        {
          params: t.Object({
            clientId: t.String(),
          }),
          detail: {
            summary: "Trust node's state to resolve conflict",
            tags: ["Admin"],
          },
        }
      )
      .post("/", ({ body }) => body, {
        body: t.Object({
          name: t.String(),
        }),
      });
  }
}
