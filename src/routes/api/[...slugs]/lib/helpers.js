// src/routes/api/[...slugs]/lib/helpers.js

/**
 * @typedef {Object} ClientIP
 * @property {string} ip - The IP address
 * @property {'ipv4'|'ipv6'} type - The IP type
 */

/**
 * @typedef {Object} StatusHistoryEntry
 * @property {Date} timestamp - When the change occurred
 * @property {boolean} from - Previous state
 * @property {boolean} to - New state
 * @property {Date} [appliedAt] - When the change was applied
 * @property {string} [userId] - Who made the change
 * @property {string} [note] - Additional information
 */

/**
 * @typedef {Object} Client
 * @property {string} id - Client identifier
 * @property {ClientIP[]} ips - List of client IPs
 * @property {string} nodeId - ID of the node this client belongs to
 * @property {Date} lastUpdate - Last time client was updated
 * @property {boolean} desiredState - What the admin wants (source of truth)
 * @property {boolean} actualState - What the node is currently reporting
 * @property {boolean} pendingSync - Whether there's a change waiting to be synced
 * @property {Date} lastStateChange - When the desired state was last changed
 * @property {Date} [lastSyncAttempt] - When we last sent an update to the node
 * @property {StatusHistoryEntry[]} statusHistory - History of status changes
 */

/**
 * @typedef {Object} ClientRequest
 * @property {string} client_id - Client identifier
 * @property {string} allowed_ips - Comma-separated list of allowed IPs
 * @property {boolean} [enabled] - Whether the client is enabled
 */

/**
 * @typedef {Object} ClientUpdate
 * @property {'status'|'config'} type - Type of update
 * @property {string} client_id - Client identifier
 * @property {boolean} enabled - Whether the client should be enabled
 * @property {string} updateId - Unique ID for this update
 * @property {Date} timestamp - When the update was created
 * @property {string} [config] - For future use
 */

/**
 * @typedef {Object} NodeUpdate
 * @property {ClientRequest[]} clients - List of clients
 */

/**
 * @typedef {Object} Node
 * @property {string} id - Node identifier (IP address)
 * @property {Date} lastSeen - Last time node was seen
 * @property {Set<string>} clientIds - Set of client IDs on this node
 */

// ---- Utility Functions ----

/**
 * Get the client IP address, handling various proxy headers and localhost
 * @param {Request} request - The HTTP request
 * @returns {string} The client IP address
 */
export const getClientIP = (request) => {
  const headers = request.headers;

  // Try common proxy headers first
  const forwardedFor = headers.get("x-forwarded-for");
  if (forwardedFor) {
    // Get the first IP in the list
    return forwardedFor.split(",")[0].trim();
  }

  const realIP = headers.get("x-real-ip");
  if (realIP) {
    return realIP;
  }

  // Get the remote address from the connection
  // In a development environment where the client is localhost
  // this might be something like ::1 or 127.0.0.1
  const remoteAddr = request.headers.get("host")?.split(":")[0] || "localhost";

  return remoteAddr;
};

// Authentication middleware
import { env } from '$env/dynamic/private';
const API_KEY = env.API_KEY || "3NZMA99jRDQA5Aa4xzZCip";

/**
 * Authenticate a request using the API key
 * @param {Object} context - The context object
 * @param {Object} context.headers - The request headers
 * @param {string} [context.headers.authorization] - The authorization header
 * @returns {boolean} True if authenticated
 * @throws {Error} If authentication fails
 */
export const authenticate = (context) => {
  const authHeader = context.headers.authorization;

  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    throw new Error("Missing or invalid Authorization header");
  }

  const token = authHeader.substring(7); // Remove 'Bearer ' prefix
  if (token !== API_KEY) {
    throw new Error("Invalid API key");
  }

  return true;
};
