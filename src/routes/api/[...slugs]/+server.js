// src/routes/api/[...slugs]/+server.js
import { Elysia } from "elysia";
import { ClientDatabase } from "./lib/database";
import { ApiController, RouteConfig } from "./lib/api";

// Create database instance
const db = new ClientDatabase();

// Create API controller
const apiController = new ApiController(db);

// Create route configuration
const routeConfig = new RouteConfig(apiController);

// Configure the app with routes
const app = routeConfig.configureRoutes(new Elysia({ prefix: "/api" }));

/**
 * Handle GET requests
 * @param {Object} params - The request parameters
 * @param {Request} params.request - The HTTP request
 * @returns {Response} The HTTP response
 */
export const GET = ({ request }) => app.handle(request);

/**
 * Handle POST requests
 * @param {Object} params - The request parameters
 * @param {Request} params.request - The HTTP request
 * @returns {Response} The HTTP response
 */
export const POST = ({ request }) => app.handle(request);
