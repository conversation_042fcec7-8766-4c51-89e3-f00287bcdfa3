<script>
  import { onMount } from 'svelte';
  
  let teamId = '';
  let count = 5;
  let loading = false;
  let result = null;
  let error = null;
  let teams = [];

  onMount(async () => {
    // Fetch available teams for the dropdown
    try {
      const response = await fetch('/api/teams');
      if (response.ok) {
        const data = await response.json();
        teams = data.teams || [];
      }
    } catch (err) {
      console.error('Failed to fetch teams:', err);
    }
  });

  async function generateTransactions() {
    if (!teamId) {
      error = 'Please select a team';
      return;
    }

    loading = true;
    error = null;
    result = null;

    try {
      const response = await fetch('/api/test/transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ teamId, count })
      });

      const data = await response.json();

      if (response.ok) {
        result = data;
      } else {
        error = data.error || 'Failed to generate transactions';
      }
    } catch (err) {
      error = 'Network error: ' + err.message;
    } finally {
      loading = false;
    }
  }
</script>

<svelte:head>
  <title>Test Transactions Generator</title>
</svelte:head>

<div class="container">
  <h1>Fake Transactions Generator</h1>
  <p>Generate fake transactions for testing the customer history page.</p>

  <form on:submit|preventDefault={generateTransactions}>
    <div class="form-group">
      <label for="teamId">Team ID:</label>
      <select id="teamId" bind:value={teamId} required>
        <option value="">Select a team...</option>
        {#each teams as team}
          <option value={team.id}>{team.id} (Balance: ${team.balance})</option>
        {/each}
      </select>
    </div>

    <div class="form-group">
      <label for="count">Number of transactions:</label>
      <input 
        type="number" 
        id="count" 
        bind:value={count} 
        min="1" 
        max="20" 
        required 
      />
    </div>

    <button type="submit" disabled={loading}>
      {loading ? 'Generating...' : 'Generate Transactions'}
    </button>
  </form>

  {#if error}
    <div class="error">
      <h3>Error:</h3>
      <p>{error}</p>
    </div>
  {/if}

  {#if result}
    <div class="success">
      <h3>Success!</h3>
      <p>{result.message}</p>
      
      {#if result.transactions}
        <h4>Generated Transactions:</h4>
        <div class="transactions">
          {#each result.transactions as transaction}
            <div class="transaction">
              <div class="transaction-header">
                <span class="date">{new Date(transaction.created_at).toLocaleDateString()}</span>
                <span class="amount {transaction.amount > 0 ? 'positive' : 'negative'}">
                  {transaction.amount > 0 ? '+' : ''}${transaction.amount}
                </span>
              </div>
              <div class="description">{transaction.description}</div>
              <div class="balance">Balance: ${transaction.balance_after}</div>
            </div>
          {/each}
        </div>
      {/if}
    </div>
  {/if}
</div>

<style>
  .container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
    font-family: system-ui, sans-serif;
  }

  h1 {
    color: #333;
    margin-bottom: 0.5rem;
  }

  p {
    color: #666;
    margin-bottom: 2rem;
  }

  .form-group {
    margin-bottom: 1rem;
  }

  label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #333;
  }

  select, input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
  }

  button {
    background: #007bff;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-size: 1rem;
    cursor: pointer;
    margin-top: 1rem;
  }

  button:hover:not(:disabled) {
    background: #0056b3;
  }

  button:disabled {
    background: #ccc;
    cursor: not-allowed;
  }

  .error {
    background: #f8d7da;
    color: #721c24;
    padding: 1rem;
    border-radius: 4px;
    margin-top: 1rem;
  }

  .success {
    background: #d4edda;
    color: #155724;
    padding: 1rem;
    border-radius: 4px;
    margin-top: 1rem;
  }

  .transactions {
    margin-top: 1rem;
  }

  .transaction {
    background: white;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 1rem;
    margin-bottom: 0.5rem;
  }

  .transaction-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .date {
    font-size: 0.9rem;
    color: #666;
  }

  .amount {
    font-weight: bold;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
  }

  .amount.positive {
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
  }

  .amount.negative {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
  }

  .description {
    font-weight: 500;
    margin-bottom: 0.25rem;
  }

  .balance {
    font-size: 0.9rem;
    color: #666;
  }
</style>
