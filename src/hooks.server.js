/**
 * This file contains server hooks for SvelteKit
 * It adds caching headers for static resources and configures request size limits
 */

// Pre-compile extension to cache rule mapping for faster lookups
const CACHE_RULES_MAP = new Map();

// Define cache durations for different file types
const CACHE_RULES = [
  // Images - long cache (1 week)
  {
    extensions: ['.png', '.jpg', '.jpeg', '.gif', '.webp', '.svg', '.ico'],
    maxAge: 7 * 24 * 60 * 60, // 7 days
    staleWhileRevalidate: 30 * 24 * 60 * 60, // 30 days
    immutable: true // These rarely change, so mark as immutable
  },
  // Fonts - very long cache (1 month)
  {
    extensions: ['.woff', '.woff2', '.ttf', '.eot'],
    maxAge: 30 * 24 * 60 * 60, // 30 days
    staleWhileRevalidate: 60 * 24 * 60 * 60, // 60 days
    immutable: true // Fonts rarely change, mark as immutable
  },
  // CSS/JS - medium cache (1 day)
  {
    extensions: ['.css', '.js'],
    maxAge: 24 * 60 * 60, // 1 day
    staleWhileRevalidate: 7 * 24 * 60 * 60, // 7 days
    immutable: false // These might change, don't mark as immutable
  },
  // Other static assets - short cache (4 hours)
  {
    extensions: ['.pdf', '.json', '.mp3', '.mp4', '.webm', '.ogg'],
    maxAge: 4 * 60 * 60, // 4 hours
    staleWhileRevalidate: 24 * 60 * 60, // 1 day
    immutable: false // These might change, don't mark as immutable
  }
];

// Build the extension map for O(1) lookups
CACHE_RULES.forEach(rule => {
  rule.extensions.forEach(ext => {
    CACHE_RULES_MAP.set(ext, rule);
  });
});

// Configure request size limits
const MAX_UPLOAD_SIZE = 400 * 1024 * 1024; // 400MB

// Helper function to create a JSON error response
/**
 * Create a JSON error response
 * @param {number} status - HTTP status code
 * @param {string} message - Error message
 * @param {string} details - Detailed error information
 * @returns {Response} HTTP response
 */
function createErrorResponse(status, message, details) {
  return new Response(
    JSON.stringify({
      success: false,
      error: message,
      details: details
    }),
    {
      status,
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
}

/** @type {import('@sveltejs/kit').Handle} */
export async function handle({ event, resolve }) {
  // Get the path from the URL
  const path = event.url.pathname;

  // Early return for API routes or paths without extensions
  if (path.startsWith('/api/')) {
    return await resolve(event);
  }

  // Special handling for APK upload endpoint
  if (event.request.method === 'POST' && (path === '/admin/custom-apk-repacker')) {
    // Clone the request to modify it
    const originalRequest = event.request;

    try {
      // Check if the content length exceeds our limit
      const contentLength = parseInt(originalRequest.headers.get('content-length') || '0', 10);

      if (contentLength > MAX_UPLOAD_SIZE) {
        return createErrorResponse(
          413,
          'File too large',
          `The uploaded file exceeds the maximum size limit of ${MAX_UPLOAD_SIZE / (1024 * 1024)}MB.`
        );
      }

      // If we get here, the file size is acceptable
      return await resolve(event);
    } catch (error) {
      console.error('Error handling large file upload:', error);
      return createErrorResponse(
        500,
        'Error processing upload',
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  // Get file extension (if any)
  const lastDotIndex = path.lastIndexOf('.');
  if (lastDotIndex === -1 || lastDotIndex === path.length - 1) {
    // No extension or ends with a dot
    return await resolve(event);
  }

  const extension = path.slice(lastDotIndex).toLowerCase();

  // Resolve the request
  const response = await resolve(event);

  // Check if we have a cache rule for this extension
  const rule = CACHE_RULES_MAP.get(extension);
  if (rule) {
    // Set cache control headers based on the matching rule
    const { maxAge, staleWhileRevalidate, immutable } = rule;
    let cacheControl = `public, max-age=${maxAge}, stale-while-revalidate=${staleWhileRevalidate}`;

    if (immutable) {
      cacheControl += ', immutable';
    }

    response.headers.set('Cache-Control', cacheControl);

    // SvelteKit handles ETags automatically, no need to add our own

    // Add Vary header to properly handle different client capabilities
    response.headers.set('Vary', 'Accept-Encoding');
  }

  return response;
}

/** @type {import('@sveltejs/kit').HandleServerError} */
export function handleError({ error, event, status = 500 }) {
  // Get the request path
  const path = event?.url?.pathname || 'unknown';
  
  // Log the error with status and path
  console.error(`[${status}] ${path}`);
  
  // Handle different error statuses
  if (status === 404) {
      return {
          message: 'Resource not found',
          code: 'NOT_FOUND',
          status: 520
      };
  }
  
  // For all other errors
  return {
      message: 'An unexpected error occurred',
      code: 'UNEXPECTED_ERROR',
      status: 520
  };
}