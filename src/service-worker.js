/// <reference types="@sveltejs/kit" />
/// <reference no-default-lib="true"/>
/// <reference lib="esnext" />
/// <reference lib="webworker" />

// Import SvelteKit service worker module
import { build, files, version } from '$service-worker';

// Create a unique cache name for this deployment
const CACHE = `phantom-cache-${version}`;

// Define the offline page path for easy reference
const OFFLINE_PAGE = '/offline.html';

// Helper function to check if a URL is in the install directory
function isInstallFile(path) {
  return path.startsWith('/install/');
}

// Note: Install files are now stored outside of the static directory
// and are served through a custom server route with authentication

// Assets to cache immediately on install
// Note: 'files' already includes everything in the static directory except those we filter out
const ASSETS = [
  ...build,  // the app itself (JS, CSS)
  ...files.filter(file => !isInstallFile(file))  // everything in the static directory except install files
];

// Additional cache for runtime requests
const RUNTIME_CACHE = `phantom-runtime-${version}`;

// Cache durations in milliseconds
const CACHE_DURATIONS = {
  images: 7 * 24 * 60 * 60 * 1000, // 7 days
  fonts: 30 * 24 * 60 * 60 * 1000, // 30 days
  styles: 24 * 60 * 60 * 1000,     // 1 day
  scripts: 24 * 60 * 60 * 1000,    // 1 day
  other: 4 * 60 * 60 * 1000        // 4 hours
};

// Helper function to determine resource type
function getResourceType(url) {
  const fileExtension = url.split('.').pop()?.toLowerCase();

  if (['png', 'jpg', 'jpeg', 'gif', 'webp', 'svg', 'ico'].includes(fileExtension)) {
    return 'images';
  } else if (['woff', 'woff2', 'ttf', 'eot'].includes(fileExtension)) {
    return 'fonts';
  } else if (fileExtension === 'css') {
    return 'styles';
  } else if (fileExtension === 'js') {
    return 'scripts';
  } else {
    return 'other';
  }
}

// Helper function to check if cache is expired
function isCacheExpired(cachedResponse, resourceType) {
  if (!cachedResponse) return true;

  const cachedTime = new Date(cachedResponse.headers.get('sw-cache-date'));
  const now = new Date();
  const maxAge = CACHE_DURATIONS[resourceType] || CACHE_DURATIONS.other;

  return (now - cachedTime) > maxAge;
}

// Use self as ServiceWorkerGlobalScope
const sw = self;

// Function to broadcast offline status to all clients
function broadcastOfflineStatus(isOffline) {
  self.clients.matchAll().then(clients => {
    clients.forEach(client => {
      client.postMessage({
        type: 'OFFLINE_STATUS',
        offline: isOffline
      });
    });
  });
}

// Install event - precache key resources
sw.addEventListener('install', (event) => {
  event.waitUntil(
    caches.open(CACHE)
      .then(cache => {
        // First, explicitly cache the offline page to ensure it's available
        return cache.add(OFFLINE_PAGE)
          .then(() => {
            // Then cache all other assets
            return cache.addAll(ASSETS)
              .catch(error => {
                // Continue even if some assets fail to cache
              });
          })
          .catch(error => {
            // Try to cache other assets even if offline page fails
            return cache.addAll(ASSETS).catch(err => {
              // Failed to cache assets
            });
          });
      })
      .then(() => {
        return sw.skipWaiting();
      })
      .catch(error => {
        // Still skip waiting even if caching fails
        return sw.skipWaiting();
      })
  );
});

// Activate event - clean up old caches
sw.addEventListener('activate', (event) => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          // Delete any old caches that don't match our current version
          if (cacheName !== CACHE && cacheName !== RUNTIME_CACHE) {
            return caches.delete(cacheName);
          }
        })
      );
    }).then(() => {
      // Claim clients so the service worker is in control immediately
      return sw.clients.claim();
    }).then(() => {
      // Check and broadcast network status immediately after activation
      return fetch(self.location.origin + '/ping', {
        method: 'HEAD',
        cache: 'no-store',
        signal: AbortSignal.timeout(2000)
      })
      .then(() => {
        // If fetch succeeds, we're online
        broadcastOfflineStatus(false);
      })
      .catch(() => {
        // If fetch fails, we're offline
        broadcastOfflineStatus(true);
      });
    })
  );
});

// Periodically check and broadcast network status using ping endpoint
setInterval(() => {
  // We can't directly check navigator.onLine in the service worker
  // So we'll use a fetch to a known endpoint to test connectivity
  fetch(self.location.origin + '/ping', {
    method: 'HEAD',
    cache: 'no-store',
    // Add a timeout to detect slow connections faster
    signal: AbortSignal.timeout(2000)
  })
    .then(() => {
      // If fetch succeeds, we're online
      broadcastOfflineStatus(false);
    })
    .catch(() => {
      // If fetch fails, we're offline
      broadcastOfflineStatus(true);
    });
}, 5000); // Check every 5 seconds for more responsive detection

// Fetch event - handle all fetch strategies
sw.addEventListener('fetch', (event) => {
  // Skip cross-origin requests
  if (!event.request.url.startsWith(sw.location.origin)) return;

  // Skip install files
  const requestUrl = new URL(event.request.url);
  if (isInstallFile(requestUrl.pathname)) return;

  // Special handling for API requests - let them fail gracefully when offline
  if (event.request.url.includes('/api/')) {
    // For API requests, we'll try the network, and if it fails, we'll return a custom response
    // This prevents uncaught errors in the UI when offline
    event.respondWith(
      fetch(event.request)
        .then(response => {
          // If we get a successful response, we're online
          broadcastOfflineStatus(false);
          return response;
        })
        .catch(error => {
          // If the request fails, we're offline
          broadcastOfflineStatus(true);

          // Return a JSON response with an error message
          return new Response(JSON.stringify({
            error: 'You are currently offline. Please check your connection and try again.',
            offline: true,
            // Return empty data structures to prevent UI errors
            clients: [],
            data: {},
            results: []
          }), {
            status: 503,
            headers: {
              'Content-Type': 'application/json'
            }
          });
        })
    );
    return;
  }

  // Handle GET requests only
  if (event.request.method !== 'GET') return;

  // Special handling for HTML pages - network first with offline fallback
  if (event.request.headers.get('accept')?.includes('text/html')) {
    event.respondWith(
      // First, check if we're testing offline mode with a specific URL
      (event.request.url.includes('non-existent-page-to-trigger-offline') ?
        Promise.reject(new Error('Forced offline mode for testing')) :
        fetch(event.request))
        .then(response => {
          // Cache the HTML response in the runtime cache
          const clonedResponse = response.clone();
          caches.open(RUNTIME_CACHE).then(cache => {
            cache.put(event.request, clonedResponse);
          });
          return response;
        })
        .catch(error => {
          // If network fails, try to return from cache
          return caches.match(event.request)
            .then(cachedResponse => {
              if (cachedResponse) {
                return cachedResponse;
              }

              // Directly serve the offline page from the static directory
              return caches.match(OFFLINE_PAGE)
                .then(offlineResponse => {
                  if (offlineResponse) {
                    return offlineResponse;
                  }

                  // As a last resort, try to fetch the offline page directly
                  return fetch(OFFLINE_PAGE)
                    .catch(() => {
                      return new Response(
                        '<!DOCTYPE html><html><head><title>Offline</title></head><body><h1>You are offline</h1><p>Please check your internet connection and try again.</p></body></html>',
                        {
                          status: 503,
                          headers: { 'Content-Type': 'text/html' }
                        }
                      );
                    });
                });
            });
        })
    );
    return;
  }

  // For static assets (in ASSETS array), use cache-first strategy
  // We already have requestUrl from earlier
  if (ASSETS.includes(requestUrl.pathname)) {
    event.respondWith(
      caches.match(requestUrl.pathname)
        .then(cachedResponse => {
          if (cachedResponse) {
            return cachedResponse;
          }
          // If not in cache (shouldn't happen for ASSETS), fetch from network
          return fetch(event.request);
        })
    );
    return;
  }

  // For all other requests, use stale-while-revalidate strategy
  const resourceType = getResourceType(event.request.url);

  event.respondWith(
    caches.open(RUNTIME_CACHE).then(cache => {
      return cache.match(event.request).then(cachedResponse => {
        // Check if we need to refresh the cache
        const fetchPromise = fetch(event.request).then(networkResponse => {
          if (networkResponse.ok) {
            // Create a copy of the response with our cache date header
            const clonedResponse = networkResponse.clone();
            const responseInit = {
              headers: new Headers(clonedResponse.headers),
              status: clonedResponse.status,
              statusText: clonedResponse.statusText
            };
            responseInit.headers.set('sw-cache-date', new Date().toISOString());

            // Read the body
            return clonedResponse.blob().then(body => {
              // Create a new response with our added header
              const newResponse = new Response(body, responseInit);
              // Put it in the cache
              cache.put(event.request, newResponse.clone());
              return newResponse;
            });
          }
          return networkResponse;
        }).catch(error => {
          // Network failed, we'll fall back to cache
        });

        // Return the cached response if we have it, otherwise wait for the network response
        if (cachedResponse) {
          // If cache is not expired, return it immediately
          if (!isCacheExpired(cachedResponse, resourceType)) {
            // Still update the cache in the background
            fetchPromise.catch(() => {});
            return cachedResponse;
          }

          // If cache is expired but we're offline, still use it
          return fetchPromise.catch(() => cachedResponse);
        }

        return fetchPromise;
      });
    })
  );
});

// Handle messages from the client
sw.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    sw.skipWaiting();
  }

  // Handle test-offline message
  if (event.data && event.data.type === 'TEST_OFFLINE') {
    // Respond to the client
    if (event.source && event.source.postMessage) {
      event.source.postMessage({
        type: 'OFFLINE_TEST_STARTED'
      });
    }
  }

  // Handle check offline status message
  if (event.data && event.data.type === 'CHECK_OFFLINE_STATUS') {
    // Check if we're online by trying to fetch a resource
    fetch(self.location.origin + '/ping', {
      method: 'HEAD',
      cache: 'no-store',
      signal: AbortSignal.timeout(2000)
    })
    .then(() => {
      // If fetch succeeds, we're online
      if (event.ports && event.ports[0]) {
        event.ports[0].postMessage({
          type: 'OFFLINE_STATUS',
          offline: false
        });
      } else if (event.source) {
        event.source.postMessage({
          type: 'OFFLINE_STATUS',
          offline: false
        });
      }
    })
    .catch(() => {
      // If fetch fails, we're offline
      if (event.ports && event.ports[0]) {
        event.ports[0].postMessage({
          type: 'OFFLINE_STATUS',
          offline: true
        });
      } else if (event.source) {
        event.source.postMessage({
          type: 'OFFLINE_STATUS',
          offline: true
        });
      }
    });
  }
});
